<?php
// Set page title
$page_title = 'Manage Episodes';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Check if TV show ID is provided
if (!isset($_GET['tvshow']) || !is_numeric($_GET['tvshow'])) {
    redirect('tvshows.php');
}

$tvshow_id = (int)$_GET['tvshow'];

// Get TV show details
$tvshow_query = "SELECT * FROM tvshows WHERE id = $tvshow_id";
$tvshow_result = mysqli_query($conn, $tvshow_query);

if (mysqli_num_rows($tvshow_result) == 0) {
    redirect('tvshows.php');
}

$tvshow = mysqli_fetch_assoc($tvshow_result);

// Process form submissions
$success_message = '';
$error_message = '';

// Add Season
if (isset($_POST['add_season'])) {
    $season_number = (int)$_POST['season_number'];

    // Check if season already exists
    $check_query = "SELECT * FROM episodes WHERE tvshow_id = $tvshow_id AND season_number = $season_number LIMIT 1";
    $check_result = mysqli_query($conn, $check_query);

    if (mysqli_num_rows($check_result) > 0) {
        $error_message = "Season $season_number already exists.";
    } else {
        // Add a placeholder episode for the season
        $add_query = "INSERT INTO episodes (tvshow_id, season_number, episode_number, title, description, created_at)
                      VALUES ($tvshow_id, $season_number, 1, 'Episode 1', 'Description for Episode 1', NOW())";

        if (mysqli_query($conn, $add_query)) {
            $success_message = "Season $season_number added successfully with a placeholder episode.";
        } else {
            $error_message = "Error adding season: " . mysqli_error($conn);
        }
    }
}

// Delete Season
if (isset($_GET['delete_season']) && is_numeric($_GET['delete_season'])) {
    $season_number = (int)$_GET['delete_season'];

    // Check if season exists
    $season_query = "SELECT COUNT(*) as count FROM episodes WHERE tvshow_id = $tvshow_id AND season_number = $season_number";
    $season_result = mysqli_query($conn, $season_query);
    $season_count = mysqli_fetch_assoc($season_result)['count'];

    if ($season_count > 0) {
        // Delete all episodes for this season
        $delete_query = "DELETE FROM episodes WHERE tvshow_id = $tvshow_id AND season_number = $season_number";

        if (mysqli_query($conn, $delete_query)) {
            $success_message = "Season $season_number and all its episodes deleted successfully.";
        } else {
            $error_message = "Error deleting season: " . mysqli_error($conn);
        }
    } else {
        $error_message = "Season not found.";
    }
}

// Add Episode
if (isset($_POST['add_episode'])) {
    $season_number = (int)$_POST['season_number'];
    $episode_number = (int)$_POST['episode_number'];
    $title = sanitize($_POST['title']);
    $quality = sanitize($_POST['quality']);
    $is_premium = isset($_POST['is_premium']) ? 1 : 0;

    // Check if episode already exists
    $check_query = "SELECT * FROM episodes WHERE tvshow_id = $tvshow_id AND season_number = $season_number AND episode_number = $episode_number";
    $check_result = mysqli_query($conn, $check_query);

    if (mysqli_num_rows($check_result) > 0) {
        $error_message = "Episode $episode_number already exists in season $season_number.";
    } else {
        // Add episode
        $add_query = "INSERT INTO episodes (tvshow_id, season_number, episode_number, title, quality, is_premium, created_at)
                     VALUES ($tvshow_id, $season_number, $episode_number, '$title', '$quality', $is_premium, NOW())";

        if (mysqli_query($conn, $add_query)) {
            $episode_id = mysqli_insert_id($conn);
            $success_message = "Episode added successfully.";

            // Redirect to manage episode links
            redirect("manage_episode_links.php?episode=$episode_id");
        } else {
            $error_message = "Error adding episode: " . mysqli_error($conn);
        }
    }
}

// Edit Episode
if (isset($_POST['edit_episode'])) {
    $episode_id = (int)$_POST['episode_id'];
    $season_number = (int)$_POST['season_number'];
    $episode_number = (int)$_POST['episode_number'];
    $title = sanitize($_POST['title']);
    $description = sanitize($_POST['description']);
    $quality = sanitize($_POST['quality']);
    $is_premium = isset($_POST['is_premium']) ? 1 : 0;

    // Check if episode exists
    $check_query = "SELECT * FROM episodes WHERE id = $episode_id AND tvshow_id = $tvshow_id";
    $check_result = mysqli_query($conn, $check_query);

    if (mysqli_num_rows($check_result) == 0) {
        $error_message = "Episode not found.";
    } else {
        // Update episode
        $update_query = "UPDATE episodes SET
                        season_number = $season_number,
                        episode_number = $episode_number,
                        title = '$title',
                        description = '$description',
                        quality = '$quality',
                        is_premium = $is_premium,
                        updated_at = NOW()
                        WHERE id = $episode_id AND tvshow_id = $tvshow_id";

        if (mysqli_query($conn, $update_query)) {
            $success_message = "Episode updated successfully.";
        } else {
            $error_message = "Error updating episode: " . mysqli_error($conn);
        }
    }
}

// Add Download Link
if (isset($_POST['add_download_link'])) {
    $episode_id = (int)$_POST['episode_id'];
    $quality = sanitize($_POST['quality']);
    $server_name = !empty($_POST['server_name']) ? sanitize($_POST['server_name']) : null;
    $file_size = !empty($_POST['file_size']) ? sanitize($_POST['file_size']) : null;
    $link_url = sanitize($_POST['link_url']);
    $is_premium = isset($_POST['is_premium']) ? 1 : 0;
    $season_number = (int)$_POST['season_number']; // Get season number for keeping accordion open

    // Check if episode exists
    $check_query = "SELECT * FROM episodes WHERE id = $episode_id AND tvshow_id = $tvshow_id";
    $check_result = mysqli_query($conn, $check_query);

    if (mysqli_num_rows($check_result) == 0) {
        $error_message = "Episode not found.";
    } else if (empty($quality) || empty($link_url)) {
        $error_message = 'Please fill in all required fields.';
    } else {
        // Check if file_size column exists
        $column_query = "SHOW COLUMNS FROM episode_links LIKE 'file_size'";
        $column_result = mysqli_query($conn, $column_query);

        if (mysqli_num_rows($column_result) > 0) {
            // Column exists, include it in the query
            $insert_query = "INSERT INTO episode_links (episode_id, link_type, quality, server_name, file_size, link_url, is_premium)
                            VALUES ($episode_id, 'download', '$quality', " . ($server_name ? "'$server_name'" : "NULL") . ", " . ($file_size ? "'$file_size'" : "NULL") . ", '$link_url', $is_premium)";
        } else {
            // Column doesn't exist, exclude it from the query
            $insert_query = "INSERT INTO episode_links (episode_id, link_type, quality, server_name, link_url, is_premium)
                            VALUES ($episode_id, 'download', '$quality', " . ($server_name ? "'$server_name'" : "NULL") . ", '$link_url', $is_premium)";
        }

        if (mysqli_query($conn, $insert_query)) {
            $success_message = 'Download link added successfully.';

            // Set active season to keep accordion open
            $_SESSION['active_season'] = $season_number;

            // Add file_size column if it doesn't exist
            if (mysqli_num_rows($column_result) == 0) {
                $add_column_query = "ALTER TABLE episode_links ADD COLUMN file_size VARCHAR(50) NULL AFTER server_name";
                mysqli_query($conn, $add_column_query);
            }
        } else {
            $error_message = 'Error adding download link: ' . mysqli_error($conn);
        }
    }
}

// Add Streaming Link
if (isset($_POST['add_stream_link'])) {
    $episode_id = (int)$_POST['episode_id'];
    $quality = sanitize($_POST['quality']);
    $server_name = !empty($_POST['server_name']) ? sanitize($_POST['server_name']) : null;
    $stream_url = sanitize($_POST['stream_url']);
    $is_premium = isset($_POST['is_premium']) ? 1 : 0;
    $season_number = (int)$_POST['season_number']; // Get season number for keeping accordion open

    // Check if episode exists
    $check_query = "SELECT * FROM episodes WHERE id = $episode_id AND tvshow_id = $tvshow_id";
    $check_result = mysqli_query($conn, $check_query);

    if (mysqli_num_rows($check_result) == 0) {
        $error_message = "Episode not found.";
    } else if (empty($quality) || empty($stream_url)) {
        $error_message = 'Please fill in all required fields.';
    } else {
        // Add new link
        $insert_query = "INSERT INTO episode_links (episode_id, link_type, quality, server_name, link_url, is_premium)
                        VALUES ($episode_id, 'stream', '$quality', " . ($server_name ? "'$server_name'" : "NULL") . ", '$stream_url', $is_premium)";

        if (mysqli_query($conn, $insert_query)) {
            $success_message = 'Streaming link added successfully.';

            // Set active season to keep accordion open
            $_SESSION['active_season'] = $season_number;
        } else {
            $error_message = 'Error adding streaming link: ' . mysqli_error($conn);
        }
    }
}

// Delete Episode
if (isset($_GET['delete_episode']) && is_numeric($_GET['delete_episode'])) {
    $episode_id = (int)$_GET['delete_episode'];

    // Check if episode has links
    $check_links_query = "SELECT COUNT(*) as count FROM episode_links WHERE episode_id = $episode_id";
    $check_links_result = mysqli_query($conn, $check_links_query);
    $links_count = mysqli_fetch_assoc($check_links_result)['count'];

    // Begin transaction
    mysqli_begin_transaction($conn);

    try {
        // Delete episode links if any
        if ($links_count > 0) {
            $delete_links_query = "DELETE FROM episode_links WHERE episode_id = $episode_id";
            if (!mysqli_query($conn, $delete_links_query)) {
                throw new Exception("Error deleting episode links: " . mysqli_error($conn));
            }
        }

        // Delete episode
        $delete_query = "DELETE FROM episodes WHERE id = $episode_id AND tvshow_id = $tvshow_id";
        if (!mysqli_query($conn, $delete_query)) {
            throw new Exception("Error deleting episode: " . mysqli_error($conn));
        }

        // Commit transaction
        mysqli_commit($conn);
        $success_message = "Episode deleted successfully.";

        // Get the season number for the deleted episode to keep the same season expanded
        $season_query = "SELECT season_number FROM episodes WHERE tvshow_id = $tvshow_id ORDER BY id DESC LIMIT 1";
        $season_result = mysqli_query($conn, $season_query);
        if (mysqli_num_rows($season_result) > 0) {
            $season_data = mysqli_fetch_assoc($season_result);
            $_SESSION['active_season'] = $season_data['season_number'];
        }
    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($conn);
        $error_message = $e->getMessage();
    }
}

// Clear active season if not coming from a delete action or adding links
if (!isset($_GET['delete_episode']) && !isset($_POST['add_download_link']) && !isset($_POST['add_stream_link'])) {
    unset($_SESSION['active_season']);
}

// Get seasons
$seasons_query = "SELECT DISTINCT season_number,
                 (SELECT COUNT(*) FROM episodes e2 WHERE e2.tvshow_id = $tvshow_id AND e2.season_number = e1.season_number) as episodes_count
                 FROM episodes e1
                 WHERE e1.tvshow_id = $tvshow_id
                 ORDER BY season_number";
$seasons_result = mysqli_query($conn, $seasons_query);

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">
            <!-- Sidebar Toggle (Topbar) -->
            <button id="sidebarToggle" class="btn btn-link d-md-none rounded-circle mr-3">
                <i class="fa fa-bars"></i>
            </button>

            <!-- Topbar Title -->
            <div class="d-none d-sm-inline-block mr-auto ml-md-3 my-2 my-md-0 mw-100">
                <h1 class="h3 mb-0 text-gray-800">Manage Episodes: <?php echo $tvshow['title']; ?></h1>
            </div>
        </nav>
    </div>

    <div class="container-fluid">
        <?php if($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-lg-4 mb-4">
                <!-- Add Season Card -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Add New Season</h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label for="season_number" class="form-label">Season Number <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="season_number" name="season_number" min="1" required>
                                <div class="invalid-feedback">
                                    Please enter a valid season number.
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" name="add_season" class="btn btn-primary">
                                    <i class="fas fa-plus-circle me-2"></i>Add Season
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Add Episode Card -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Add New Episode</h6>
                    </div>
                    <div class="card-body">
                        <?php if(mysqli_num_rows($seasons_result) > 0): ?>
                            <form method="POST" action="" class="needs-validation" novalidate>
                                <div class="mb-3">
                                    <label for="season_number" class="form-label">Season <span class="text-danger">*</span></label>
                                    <select class="form-select" id="season_number" name="season_number" required>
                                        <option value="">Select Season</option>
                                        <?php
                                        mysqli_data_seek($seasons_result, 0);
                                        while($season = mysqli_fetch_assoc($seasons_result)):
                                        ?>
                                        <option value="<?php echo $season['season_number']; ?>">Season <?php echo $season['season_number']; ?> (<?php echo $season['episodes_count']; ?> episodes)</option>
                                        <?php endwhile; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a season.
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="episode_number" class="form-label">Episode Number <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="episode_number" name="episode_number" min="1" required>
                                    <div class="invalid-feedback">
                                        Please enter a valid episode number.
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="title" class="form-label">Episode Title <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="title" name="title" required>
                                    <div class="invalid-feedback">
                                        Please enter an episode title.
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="quality" class="form-label">Quality</label>
                                    <select class="form-select" id="quality" name="quality">
                                        <option value="">Select Quality</option>
                                        <option value="SD">SD</option>
                                        <option value="HD">HD</option>
                                        <option value="Full HD">Full HD</option>
                                        <option value="4K">4K</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_premium" name="is_premium">
                                        <label class="form-check-label" for="is_premium">Premium Only</label>
                                    </div>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" name="add_episode" class="btn btn-primary">
                                        <i class="fas fa-plus-circle me-2"></i>Add Episode
                                    </button>
                                </div>
                            </form>
                        <?php else: ?>
                            <div class="alert alert-warning mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Please add a season first before adding episodes.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- TV Show Info Card -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">TV Show Info</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex mb-3">
                            <?php if (!empty($tvshow['poster'])): ?>
                                <?php if (strpos($tvshow['poster'], 'http') === 0): ?>
                                    <!-- External URL (TMDB) -->
                                    <img src="<?php echo $tvshow['poster']; ?>" alt="" class="me-3" style="width: 80px; height: 120px; object-fit: cover;" onerror="this.onerror=null; this.src='assets/img/default-poster.jpg';">
                                <?php elseif (file_exists('../uploads/' . $tvshow['poster'])): ?>
                                    <!-- Local file -->
                                    <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $tvshow['poster']; ?>" alt="" class="me-3" style="width: 80px; height: 120px; object-fit: cover;">
                                <?php else: ?>
                                    <!-- Try TMDB path -->
                                    <img src="https://image.tmdb.org/t/p/w92<?php echo $tvshow['poster']; ?>" alt="" class="me-3" style="width: 80px; height: 120px; object-fit: cover;" onerror="this.onerror=null; this.src='assets/img/default-poster.jpg';">
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="me-3" style="width: 80px; height: 120px; background-color: #f8f9fc; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-tv fa-2x text-muted"></i>
                                </div>
                            <?php endif; ?>

                            <div>
                                <h5 class="mb-1"><?php echo $tvshow['title']; ?></h5>
                                <p class="mb-1">
                                    <span class="badge bg-primary"><?php echo $tvshow['start_year']; ?><?php echo $tvshow['end_year'] ? ' - ' . $tvshow['end_year'] : ' - Present'; ?></span>
                                    <?php if($tvshow['premium_only']): ?>
                                    <span class="badge bg-danger">Premium</span>
                                    <?php endif; ?>
                                </p>
                                <p class="mb-0">
                                    <a href="edit_tvshow.php?id=<?php echo $tvshow_id; ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit me-1"></i> Edit TV Show
                                    </a>
                                    <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $tvshow_id; ?>" class="btn btn-sm btn-success" target="_blank">
                                        <i class="fas fa-eye me-1"></i> View
                                    </a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-8 mb-4">
                <!-- Seasons and Episodes List -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Seasons & Episodes</h6>
                    </div>
                    <div class="card-body">
                        <?php if(mysqli_num_rows($seasons_result) > 0): ?>
                            <div class="accordion" id="seasonsAccordion">
                                <?php
                                mysqli_data_seek($seasons_result, 0);
                                while($season = mysqli_fetch_assoc($seasons_result)):
                                ?>
                                <div class="accordion-item mb-3">
                                    <h2 class="accordion-header" id="heading<?php echo $season['season_number']; ?>">
                                        <button class="accordion-button <?php echo (isset($_SESSION['active_season']) && $_SESSION['active_season'] == $season['season_number']) ? '' : 'collapsed'; ?>" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?php echo $season['season_number']; ?>" aria-expanded="<?php echo (isset($_SESSION['active_season']) && $_SESSION['active_season'] == $season['season_number']) ? 'true' : 'false'; ?>" aria-controls="collapse<?php echo $season['season_number']; ?>">
                                            <div class="d-flex justify-content-between align-items-center w-100 me-3">
                                                <span>Season <?php echo $season['season_number']; ?></span>
                                                <span class="badge bg-primary"><?php echo $season['episodes_count']; ?> Episodes</span>
                                            </div>
                                        </button>
                                    </h2>
                                    <div id="collapse<?php echo $season['season_number']; ?>" class="accordion-collapse collapse <?php echo (isset($_SESSION['active_season']) && $_SESSION['active_season'] == $season['season_number']) ? 'show' : ''; ?>" aria-labelledby="heading<?php echo $season['season_number']; ?>" data-bs-parent="#seasonsAccordion">
                                        <div class="accordion-body">
                                            <div class="d-flex justify-content-between mb-3">
                                                <h6 class="mb-0">Episodes</h6>
                                                <a href="manage_episodes.php?delete_season=<?php echo $season['season_number']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this season and all its episodes?')">
                                                    <i class="fas fa-trash me-1"></i> Delete Season
                                                </a>
                                            </div>

                                            <?php
                                            // Get episodes for this season
                                            $episodes_query = "SELECT e.* FROM episodes e WHERE e.tvshow_id = $tvshow_id AND e.season_number = {$season['season_number']} ORDER BY e.episode_number";
                                            $episodes_result = mysqli_query($conn, $episodes_query);
                                            ?>

                                            <?php if(mysqli_num_rows($episodes_result) > 0): ?>
                                                <div class="table-responsive">
                                                    <table class="table table-hover align-middle">
                                                        <thead class="table-light">
                                                            <tr>
                                                                <th>Episode</th>
                                                                <th>Title</th>
                                                                <th>Quality</th>
                                                                <th>Premium</th>
                                                                <th>Links</th>
                                                                <th>Actions</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <?php while($episode = mysqli_fetch_assoc($episodes_result)): ?>
                                                            <?php
                                                            // Get links count for this episode
                                                            $links_query = "SELECT COUNT(*) as count FROM episode_links WHERE episode_id = {$episode['id']}";
                                                            $links_result = mysqli_query($conn, $links_query);
                                                            $links_count = mysqli_fetch_assoc($links_result)['count'];
                                                            ?>
                                                            <tr>
                                                                <td><?php echo $episode['episode_number']; ?></td>
                                                                <td><?php echo $episode['title']; ?></td>
                                                                <td><?php echo $episode['quality']; ?></td>
                                                                <td>
                                                                    <?php if($episode['is_premium']): ?>
                                                                    <span class="badge bg-danger">Yes</span>
                                                                    <?php else: ?>
                                                                    <span class="badge bg-secondary">No</span>
                                                                    <?php endif; ?>
                                                                </td>
                                                                <td>
                                                                    <span class="badge bg-info"><?php echo $links_count; ?> Links</span>
                                                                </td>
                                                                <td>
                                                                    <div class="btn-group">
                                                                        <a href="manage_episode_links.php?episode=<?php echo $episode['id']; ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Manage Links">
                                                                            <i class="fas fa-link"></i>
                                                                        </a>
                                                                        <button type="button" class="btn btn-sm btn-success add-link" data-bs-toggle="modal" data-bs-target="#addLinkModal"
                                                                            data-id="<?php echo $episode['id']; ?>"
                                                                            data-title="<?php echo htmlspecialchars($episode['title']); ?>"
                                                                            data-season="<?php echo $episode['season_number']; ?>"
                                                                            data-episode="<?php echo $episode['episode_number']; ?>">
                                                                            <i class="fas fa-plus"></i>
                                                                        </button>
                                                                        <button type="button" class="btn btn-sm btn-info edit-episode" data-bs-toggle="modal" data-bs-target="#editEpisodeModal"
                                                                            data-id="<?php echo $episode['id']; ?>"
                                                                            data-season="<?php echo $episode['season_number']; ?>"
                                                                            data-episode="<?php echo $episode['episode_number']; ?>"
                                                                            data-title="<?php echo htmlspecialchars($episode['title']); ?>"
                                                                            data-description="<?php echo htmlspecialchars($episode['description']); ?>"
                                                                            data-quality="<?php echo $episode['quality']; ?>"
                                                                            data-premium="<?php echo $episode['is_premium']; ?>">
                                                                            <i class="fas fa-edit"></i>
                                                                        </button>
                                                                        <a href="manage_episodes.php?tvshow=<?php echo $tvshow_id; ?>&delete_episode=<?php echo $episode['id']; ?>" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="Delete" onclick="return confirm('Are you sure you want to delete this episode?')">
                                                                            <i class="fas fa-trash"></i>
                                                                        </a>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <?php endwhile; ?>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            <?php else: ?>
                                                <div class="alert alert-info mb-0">
                                                    <i class="fas fa-info-circle me-2"></i>
                                                    No episodes found for this season. Add episodes using the form on the left.
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endwhile; ?>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                No seasons found. Add a season using the form on the left.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Episode Modal -->
<div class="modal fade" id="editEpisodeModal" tabindex="-1" aria-labelledby="editEpisodeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editEpisodeModalLabel">Edit Episode</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="episode_id" id="edit_episode_id">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_season_number" class="form-label">Season Number <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="edit_season_number" name="season_number" min="1" required>
                                <div class="invalid-feedback">
                                    Please enter a valid season number.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_episode_number" class="form-label">Episode Number <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="edit_episode_number" name="episode_number" min="1" required>
                                <div class="invalid-feedback">
                                    Please enter a valid episode number.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_title" class="form-label">Episode Title <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_title" name="title" required>
                        <div class="invalid-feedback">
                            Please enter an episode title.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="edit_quality" class="form-label">Quality</label>
                        <select class="form-select" id="edit_quality" name="quality">
                            <option value="">Select Quality</option>
                            <option value="SD">SD</option>
                            <option value="HD">HD</option>
                            <option value="Full HD">Full HD</option>
                            <option value="4K">4K</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="edit_is_premium" name="is_premium">
                            <label class="form-check-label" for="edit_is_premium">Premium Only</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="edit_episode" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Link Modal -->
<div class="modal fade" id="addLinkModal" tabindex="-1" aria-labelledby="addLinkModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addLinkModalLabel">Add Link</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="link_episode_id" value="">

                <!-- Episode Info -->
                <div class="alert alert-info mb-3">
                    <div id="episode_info"></div>
                </div>

                <!-- Nav tabs -->
                <ul class="nav nav-tabs" id="linkTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="download-tab" data-bs-toggle="tab" data-bs-target="#download-tab-pane" type="button" role="tab" aria-controls="download-tab-pane" aria-selected="true">ডাউনলোড লিংক</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="stream-tab" data-bs-toggle="tab" data-bs-target="#stream-tab-pane" type="button" role="tab" aria-controls="stream-tab-pane" aria-selected="false">স্ট্রিমিং লিংক</button>
                    </li>
                </ul>

                <!-- Tab content -->
                <div class="tab-content pt-3" id="linkTabsContent">
                    <!-- Download Tab -->
                    <div class="tab-pane fade show active" id="download-tab-pane" role="tabpanel" aria-labelledby="download-tab" tabindex="0">
                        <form method="POST" action="" id="downloadLinkForm" class="needs-validation" novalidate>
                            <input type="hidden" name="episode_id" id="download_episode_id">
                            <input type="hidden" name="season_number" id="download_season_number">

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="download_quality" class="form-label">কোয়ালিটি <span class="text-danger">*</span></label>
                                    <select class="form-select" id="download_quality" name="quality" required>
                                        <option value="">কোয়ালিটি নির্বাচন করুন</option>
                                        <option value="480p">480p</option>
                                        <option value="720p">720p</option>
                                        <option value="1080p">1080p</option>
                                        <option value="2160p">2160p (4K)</option>
                                        <option value="SD">SD</option>
                                        <option value="HD">HD</option>
                                        <option value="Full HD">Full HD</option>
                                        <option value="4K">4K</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        কোয়ালিটি নির্বাচন করুন
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label for="download_server_name" class="form-label">সার্ভার নাম</label>
                                    <input type="text" class="form-control" id="download_server_name" name="server_name" placeholder="যেমন: Google Drive, Mega">
                                </div>
                                <div class="col-md-4">
                                    <label for="download_file_size" class="form-label">ফাইল সাইজ</label>
                                    <input type="text" class="form-control" id="download_file_size" name="file_size" placeholder="যেমন: 700MB, 1.4GB">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="download_link_url" class="form-label">ডাউনলোড লিংক <span class="text-danger">*</span></label>
                                <input type="url" class="form-control" id="download_link_url" name="link_url" required>
                                <div class="invalid-feedback">
                                    ডাউনলোড লিংক দিন
                                </div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="download_is_premium" name="is_premium">
                                <label class="form-check-label" for="download_is_premium">শুধুমাত্র প্রিমিয়াম</label>
                            </div>

                            <div class="d-grid">
                                <button type="submit" name="add_download_link" class="btn btn-primary">ডাউনলোড লিংক যোগ করুন</button>
                            </div>
                        </form>
                    </div>

                    <!-- Stream Tab -->
                    <div class="tab-pane fade" id="stream-tab-pane" role="tabpanel" aria-labelledby="stream-tab" tabindex="0">
                        <form method="POST" action="" id="streamLinkForm" class="needs-validation" novalidate>
                            <input type="hidden" name="episode_id" id="stream_episode_id">
                            <input type="hidden" name="season_number" id="stream_season_number">

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="stream_quality" class="form-label">কোয়ালিটি <span class="text-danger">*</span></label>
                                    <select class="form-select" id="stream_quality" name="quality" required>
                                        <option value="">কোয়ালিটি নির্বাচন করুন</option>
                                        <option value="480p">480p</option>
                                        <option value="720p">720p</option>
                                        <option value="1080p">1080p</option>
                                        <option value="2160p">2160p (4K)</option>
                                        <option value="SD">SD</option>
                                        <option value="HD">HD</option>
                                        <option value="Full HD">Full HD</option>
                                        <option value="4K">4K</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        কোয়ালিটি নির্বাচন করুন
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="stream_server_name" class="form-label">সার্ভার নাম</label>
                                    <input type="text" class="form-control" id="stream_server_name" name="server_name" placeholder="যেমন: Server 1, Cloudflare">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="stream_url" class="form-label">স্ট্রিমিং লিংক <span class="text-danger">*</span></label>
                                <input type="url" class="form-control" id="stream_url" name="stream_url" required>
                                <div class="invalid-feedback">
                                    স্ট্রিমিং লিংক দিন
                                </div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="stream_is_premium" name="is_premium">
                                <label class="form-check-label" for="stream_is_premium">শুধুমাত্র প্রিমিয়াম</label>
                            </div>

                            <div class="d-grid">
                                <button type="submit" name="add_stream_link" class="btn btn-primary">স্ট্রিমিং লিংক যোগ করুন</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize edit episode modal
document.addEventListener('DOMContentLoaded', function() {
    const editButtons = document.querySelectorAll('.edit-episode');

    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const season = this.getAttribute('data-season');
            const episode = this.getAttribute('data-episode');
            const title = this.getAttribute('data-title');
            const description = this.getAttribute('data-description');
            const quality = this.getAttribute('data-quality');
            const premium = this.getAttribute('data-premium');

            document.getElementById('edit_episode_id').value = id;
            document.getElementById('edit_season_number').value = season;
            document.getElementById('edit_episode_number').value = episode;
            document.getElementById('edit_title').value = title;
            document.getElementById('edit_description').value = description;
            document.getElementById('edit_quality').value = quality;
            document.getElementById('edit_is_premium').checked = premium === '1';
        });
    });

    // Initialize add link modal
    const addLinkButtons = document.querySelectorAll('.add-link');

    addLinkButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const title = this.getAttribute('data-title');
            const season = this.getAttribute('data-season');
            const episode = this.getAttribute('data-episode');

            // Set episode info
            document.getElementById('episode_info').innerHTML = `<strong>সিজন ${season}, এপিসোড ${episode}:</strong> ${title}`;

            // Set episode ID and season number in both forms
            document.getElementById('download_episode_id').value = id;
            document.getElementById('stream_episode_id').value = id;
            document.getElementById('download_season_number').value = season;
            document.getElementById('stream_season_number').value = season;
        });
    });
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
