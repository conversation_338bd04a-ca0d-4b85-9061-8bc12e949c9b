<?php
require_once 'includes/header.php';
require_once 'includes/ad_placeholder.php';

// Get featured movies (if no featured movies, get latest ones)
$featured_movies_query = "SELECT m.*, c.name as category_name,
                         GROUP_CONCAT(t.name ORDER BY t.name SEPARATOR ',') as tags,
                         GROUP_CONCAT(t.id ORDER BY t.name SEPARATOR ',') as tag_ids
                         FROM movies m
                         LEFT JOIN categories c ON m.category_id = c.id
                         LEFT JOIN movie_tags mt ON m.id = mt.movie_id
                         LEFT JOIN tags t ON mt.tag_id = t.id
                         WHERE m.featured = 1
                         GROUP BY m.id
                         ORDER BY m.release_year DESC
                         LIMIT 10";
$featured_movies_result = mysqli_query($conn, $featured_movies_query);

// If no featured movies found, use latest movies instead
if (mysqli_num_rows($featured_movies_result) == 0) {
    $featured_movies_query = "SELECT m.*, c.name as category_name,
                             GROUP_CONCAT(t.name ORDER BY t.name SEPARATOR ',') as tags,
                             GROUP_CONCAT(t.id ORDER BY t.name SEPARATOR ',') as tag_ids
                             FROM movies m
                             LEFT JOIN categories c ON m.category_id = c.id
                             LEFT JOIN movie_tags mt ON m.id = mt.movie_id
                             LEFT JOIN tags t ON mt.tag_id = t.id
                             GROUP BY m.id
                             ORDER BY m.created_at DESC
                             LIMIT 5";
    $featured_movies_result = mysqli_query($conn, $featured_movies_query);
}

// Get featured TV shows (if no featured shows, get latest ones)
$featured_tvshows_query = "SELECT t.*, c.name as category_name,
                          GROUP_CONCAT(tg.name ORDER BY tg.name SEPARATOR ',') as tags,
                          GROUP_CONCAT(tg.id ORDER BY tg.name SEPARATOR ',') as tag_ids
                          FROM tvshows t
                          LEFT JOIN categories c ON t.category_id = c.id
                          LEFT JOIN tvshow_tags tt ON t.id = tt.tvshow_id
                          LEFT JOIN tags tg ON tt.tag_id = tg.id
                          WHERE t.featured = 1
                          GROUP BY t.id
                          ORDER BY t.start_year DESC
                          LIMIT 10";
$featured_tvshows_result = mysqli_query($conn, $featured_tvshows_query);

// If no featured TV shows found, use latest TV shows instead
if (mysqli_num_rows($featured_tvshows_result) == 0) {
    $featured_tvshows_query = "SELECT t.*, c.name as category_name,
                              GROUP_CONCAT(tg.name ORDER BY tg.name SEPARATOR ',') as tags,
                              GROUP_CONCAT(tg.id ORDER BY tg.name SEPARATOR ',') as tag_ids
                              FROM tvshows t
                              LEFT JOIN categories c ON t.category_id = c.id
                              LEFT JOIN tvshow_tags tt ON t.id = tt.tvshow_id
                              LEFT JOIN tags tg ON tt.tag_id = tg.id
                              GROUP BY t.id
                              ORDER BY t.created_at DESC
                              LIMIT 5";
    $featured_tvshows_result = mysqli_query($conn, $featured_tvshows_query);
}

// Get latest movies
$latest_movies_query = "SELECT m.*, c.name as category_name,
                       GROUP_CONCAT(t.name ORDER BY t.name SEPARATOR ',') as tags,
                       GROUP_CONCAT(t.id ORDER BY t.name SEPARATOR ',') as tag_ids
                       FROM movies m
                       LEFT JOIN categories c ON m.category_id = c.id
                       LEFT JOIN movie_tags mt ON m.id = mt.movie_id
                       LEFT JOIN tags t ON mt.tag_id = t.id
                       GROUP BY m.id
                       ORDER BY m.created_at DESC
                       LIMIT 18";
$latest_movies_result = mysqli_query($conn, $latest_movies_query);

// Get latest TV shows
$latest_tvshows_query = "SELECT t.*, c.name as category_name,
                        GROUP_CONCAT(tg.name ORDER BY tg.name SEPARATOR ',') as tags,
                        GROUP_CONCAT(tg.id ORDER BY tg.name SEPARATOR ',') as tag_ids
                        FROM tvshows t
                        LEFT JOIN categories c ON t.category_id = c.id
                        LEFT JOIN tvshow_tags tt ON t.id = tt.tvshow_id
                        LEFT JOIN tags tg ON tt.tag_id = tg.id
                        GROUP BY t.id
                        ORDER BY t.created_at DESC
                        LIMIT 18";
$latest_tvshows_result = mysqli_query($conn, $latest_tvshows_query);

// Get top 10 movies by rating
$top_movies_query = "SELECT m.*, c.name as category_name,
                    GROUP_CONCAT(t.name ORDER BY t.name SEPARATOR ',') as tags,
                    GROUP_CONCAT(t.id ORDER BY t.name SEPARATOR ',') as tag_ids
                    FROM movies m
                    LEFT JOIN categories c ON m.category_id = c.id
                    LEFT JOIN movie_tags mt ON m.id = mt.movie_id
                    LEFT JOIN tags t ON mt.tag_id = t.id
                    GROUP BY m.id
                    ORDER BY m.rating DESC
                    LIMIT 10";
$top_movies_result = mysqli_query($conn, $top_movies_query);

// Get top 10 TV shows by rating
$top_tvshows_query = "SELECT t.*, c.name as category_name,
                     GROUP_CONCAT(tg.name ORDER BY tg.name SEPARATOR ',') as tags,
                     GROUP_CONCAT(tg.id ORDER BY tg.name SEPARATOR ',') as tag_ids
                     FROM tvshows t
                     LEFT JOIN categories c ON t.category_id = c.id
                     LEFT JOIN tvshow_tags tt ON t.id = tt.tvshow_id
                     LEFT JOIN tags tg ON tt.tag_id = tg.id
                     GROUP BY t.id
                     ORDER BY t.rating DESC
                     LIMIT 10";
$top_tvshows_result = mysqli_query($conn, $top_tvshows_query);

// Get premium content
$premium_content_query = "SELECT m.id, m.title, m.poster, m.release_year, m.rating, 'movie' as content_type, c.name as category_name,
                         GROUP_CONCAT(t.name ORDER BY t.name SEPARATOR ',') as tags,
                         GROUP_CONCAT(t.id ORDER BY t.name SEPARATOR ',') as tag_ids
                         FROM movies m
                         LEFT JOIN categories c ON m.category_id = c.id
                         LEFT JOIN movie_tags mt ON m.id = mt.movie_id
                         LEFT JOIN tags t ON mt.tag_id = t.id
                         WHERE m.premium_only = 1
                         GROUP BY m.id
                         UNION
                         SELECT t.id, t.title, t.poster, t.start_year, t.rating, 'tvshow' as content_type, c.name as category_name,
                         GROUP_CONCAT(tg.name ORDER BY tg.name SEPARATOR ',') as tags,
                         GROUP_CONCAT(tg.id ORDER BY tg.name SEPARATOR ',') as tag_ids
                         FROM tvshows t
                         LEFT JOIN categories c ON t.category_id = c.id
                         LEFT JOIN tvshow_tags tt ON t.id = tt.tvshow_id
                         LEFT JOIN tags tg ON tt.tag_id = tg.id
                         WHERE t.premium_only = 1
                         GROUP BY t.id
                         ORDER BY rating DESC
                         LIMIT 8";
$premium_content_result = mysqli_query($conn, $premium_content_query);
?>

<!-- Main Slider Section -->
<section class="main-slider">
    <div class="slider-wrapper">
        <?php
        // Get latest movies and TV shows for slider
        $slider_query = "SELECT
                            'movie' as content_type,
                            m.id,
                            m.title,
                            m.description,
                            m.release_year as year,
                            m.duration,
                            m.banner,
                            m.poster,
                            m.rating,
                            m.premium_only,
                            c.name as category_name
                        FROM movies m
                        LEFT JOIN categories c ON m.category_id = c.id
                        WHERE m.featured = 1 OR m.rating >= 8.0
                        UNION
                        SELECT
                            'tvshow' as content_type,
                            t.id,
                            t.title,
                            t.description,
                            t.start_year as year,
                            NULL as duration,
                            t.banner,
                            t.poster,
                            t.rating,
                            t.premium_only,
                            c.name as category_name
                        FROM tvshows t
                        LEFT JOIN categories c ON t.category_id = c.id
                        WHERE t.featured = 1 OR t.rating >= 8.0
                        ORDER BY year DESC
                        LIMIT 5";
        $slider_result = mysqli_query($conn, $slider_query);

        $slide_count = 0;
        while($content = mysqli_fetch_assoc($slider_result)):
            $slide_count++;
            // Check if banner exists, if not use a default image
            $banner_image = !empty($content['banner']) && file_exists('uploads/' . $content['banner']) ? $content['banner'] : 'movie_banner_564147_1743844083.jpg';
            $content_type = $content['content_type'];
            $content_id = $content['id'];
        ?>
        <div class="slide<?php echo ($slide_count == 1) ? ' active' : ''; ?>">
            <div class="slide-image" style="background-image: url('<?php echo SITE_URL; ?>/uploads/<?php echo $banner_image; ?>')"></div>
            <div class="slide-overlay">
                <div class="container h-100">
                    <div class="row h-100 align-items-center">
                        <div class="col-lg-6 col-md-8">
                            <div class="slide-content">
                                <div class="d-flex align-items-center mb-3">
                                    <?php if($content['premium_only']): ?>
                                    <div class="premium-tag me-3"><i class="fas fa-crown"></i> PREMIUM</div>
                                    <?php endif; ?>
                                    <div class="content-type-badge">
                                        <?php if($content_type == 'movie'): ?>
                                        <span class="badge bg-danger"><i class="fas fa-film me-1"></i> Movie</span>
                                        <?php else: ?>
                                        <span class="badge bg-primary"><i class="fas fa-tv me-1"></i> TV Show</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <h1 class="slide-title"><?php echo htmlspecialchars($content['title']); ?></h1>
                                <div class="slide-meta">
                                    <span><i class="fas fa-calendar"></i> <?php echo $content['year']; ?></span>
                                    <?php if($content_type == 'movie' && !empty($content['duration'])): ?>
                                    <span><i class="fas fa-clock"></i> <?php echo $content['duration']; ?> min</span>
                                    <?php endif; ?>
                                    <span><i class="fas fa-star"></i> <?php echo number_format($content['rating'], 1); ?></span>
                                    <span><i class="fas fa-tag"></i> <?php echo htmlspecialchars($content['category_name']); ?></span>
                                </div>
                                <p class="slide-description"><?php echo htmlspecialchars(substr($content['description'], 0, 200)); ?>...</p>
                                <div class="slide-buttons">
                                    <?php if($content_type == 'movie'): ?>
                                    <a href="<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $content_id; ?>" class="btn btn-danger btn-lg"><i class="fas fa-play me-2"></i> এখনই দেখুন</a>
                                    <a href="<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $content_id; ?>#download" class="btn btn-outline-light btn-lg"><i class="fas fa-download me-2"></i> ডাউনলোড</a>
                                    <?php else: ?>
                                    <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $content_id; ?>" class="btn btn-danger btn-lg"><i class="fas fa-play me-2"></i> এখনই দেখুন</a>
                                    <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $content_id; ?>#download" class="btn btn-outline-light btn-lg"><i class="fas fa-download me-2"></i> ডাউনলোড</a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endwhile; ?>
    </div>
    <div class="slider-controls">
        <button class="slider-prev"><i class="fas fa-chevron-left"></i></button>
        <div class="slider-dots">
            <?php for($i = 1; $i <= $slide_count; $i++): ?>
            <div class="slider-dot<?php echo ($i == 1) ? ' active' : ''; ?>" data-slide="<?php echo $i; ?>"></div>
            <?php endfor; ?>
        </div>
        <button class="slider-next"><i class="fas fa-chevron-right"></i></button>
    </div>
</section>

<!-- Top Banner Ad -->
<?php if (!(isLoggedIn() && isPremium())): ?>
<div class="container my-4">
    <div class="ad-container ad-banner">
        <script type="text/javascript">
            atOptions = {
                'key' : '735a559a5872816da47237a603cac4ad',
                'format' : 'iframe',
                'height' : 90,
                'width' : 728,
                'params' : {}
            };
        </script>
        <script type="text/javascript" src="//www.highperformanceformat.com/735a559a5872816da47237a603cac4ad/invoke.js"></script>
    </div>
</div>
<?php endif; ?>

<!-- Top 10 Movies Section -->
<section class="py-5">
    <div class="container">
        <h2 class="section-title mb-4"><i class="fas fa-trophy text-warning me-2"></i>সপ্তাহের সেরা ১০ মুভি</h2>
        <div class="row">
            <div class="col-12">
                <div class="top-10-carousel">
                    <?php
                    $counter = 1;
                    while($movie = mysqli_fetch_assoc($top_movies_result)):
                    ?>
                    <div class="top-10-item">
                        <div class="top-10-rank">
                            <span class="rank-number"><?php echo $counter; ?></span>
                        </div>
                        <div class="movie-card" data-category="<?php echo $movie['category_id']; ?>">
                            <?php if($movie['premium_only']): ?>
                            <div class="premium-badge">
                                <i class="fas fa-crown"></i> Premium
                                <span class="quality">HD</span>
                            </div>
                            <?php endif; ?>
                            <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $movie['poster']; ?>" alt="<?php echo htmlspecialchars($movie['title']); ?>" loading="lazy">
                            <a href="<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $movie['id']; ?>" class="card-link"></a>
                            <div class="movie-card-overlay">
                                <h5 class="movie-card-title"><?php echo htmlspecialchars($movie['title']); ?></h5>
                                <div class="movie-card-info">
                                    <span><?php echo $movie['release_year']; ?></span> •
                                    <span><?php echo htmlspecialchars($movie['category_name']); ?></span>
                                </div>
                                <div class="movie-card-rating">
                                    <i class="fas fa-star"></i> <?php echo number_format($movie['rating'], 1); ?>
                                </div>
                                <a href="<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $movie['id']; ?>" class="btn btn-danger btn-sm mt-2">এখনই দেখুন</a>
                            </div>
                        </div>
                    </div>
                    <?php
                    $counter++;
                    endwhile;
                    ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Inline Ad After Top Movies -->
<?php if (!(isLoggedIn() && isPremium())): ?>
<div class="container my-4">
    <div class="ad-container ad-inline">
        <script type="text/javascript">
            atOptions = {
                'key' : 'ceac305b755cbace9181e4d593e3700b',
                'format' : 'iframe',
                'height' : 60,
                'width' : 468,
                'params' : {}
            };
        </script>
        <script type="text/javascript" src="//www.highperformanceformat.com/ceac305b755cbace9181e4d593e3700b/invoke.js"></script>
    </div>
</div>
<?php endif; ?>

<!-- Top 10 TV Shows Section -->
<section class="py-5 bg-dark">
    <div class="container">
        <h2 class="section-title mb-4"><i class="fas fa-tv text-primary me-2"></i>সপ্তাহের সেরা ১০ টিভি শো</h2>
        <div class="row">
            <div class="col-12">
                <div class="top-10-carousel">
                    <?php
                    $counter = 1;
                    while($tvshow = mysqli_fetch_assoc($top_tvshows_result)):
                    ?>
                    <div class="top-10-item">
                        <div class="top-10-rank">
                            <span class="rank-number"><?php echo $counter; ?></span>
                        </div>
                        <div class="movie-card" data-category="<?php echo $tvshow['category_id']; ?>">
                            <?php if($tvshow['premium_only']): ?>
                            <div class="premium-badge">
                                <i class="fas fa-crown"></i> Premium
                                <span class="quality">HD</span>
                            </div>
                            <?php endif; ?>
                            <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $tvshow['poster']; ?>" alt="<?php echo htmlspecialchars($tvshow['title']); ?>" loading="lazy">
                            <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $tvshow['id']; ?>" class="card-link"></a>
                            <div class="movie-card-overlay">
                                <h5 class="movie-card-title"><?php echo htmlspecialchars($tvshow['title']); ?></h5>
                                <div class="movie-card-info">
                                    <span><?php echo $tvshow['start_year']; ?><?php echo $tvshow['end_year'] ? ' - ' . $tvshow['end_year'] : ' - Present'; ?></span> •
                                    <span><?php echo htmlspecialchars($tvshow['category_name']); ?></span>
                                </div>
                                <div class="movie-card-rating">
                                    <i class="fas fa-star"></i> <?php echo number_format($tvshow['rating'], 1); ?>
                                </div>
                                <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $tvshow['id']; ?>" class="btn btn-danger btn-sm mt-2">এখনই দেখুন</a>
                            </div>
                        </div>
                    </div>
                    <?php
                    $counter++;
                    endwhile;
                    ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Sidebar Ad Section -->
<?php if (!(isLoggedIn() && isPremium())): ?>
<div class="container my-4">
    <div class="row">
        <div class="col-lg-8">
            <div class="ad-container ad-inline">
                <script type="text/javascript">
                    atOptions = {
                        'key' : '735a559a5872816da47237a603cac4ad',
                        'format' : 'iframe',
                        'height' : 90,
                        'width' : 728,
                        'params' : {}
                    };
                </script>
                <script type="text/javascript" src="//www.highperformanceformat.com/735a559a5872816da47237a603cac4ad/invoke.js"></script>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="ad-container ad-sidebar">
                <script type="text/javascript">
                    atOptions = {
                        'key' : '7d3b7accac0194a88ccf420c241ec7aa',
                        'format' : 'iframe',
                        'height' : 250,
                        'width' : 300,
                        'params' : {}
                    };
                </script>
                <script type="text/javascript" src="//www.highperformanceformat.com/7d3b7accac0194a88ccf420c241ec7aa/invoke.js"></script>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Featured Movies Section -->
<section class="py-5">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="section-title mb-0"><i class="fas fa-star text-warning me-2"></i>ফিচার্ড মুভি</h2>
            <a href="<?php echo SITE_URL; ?>/movies.php" class="btn btn-outline-danger">সব দেখুন <i class="fas fa-arrow-right ms-1"></i></a>
        </div>
        <div class="featured-carousel owl-carousel">
            <?php
            // Reset the featured movies result pointer
            mysqli_data_seek($featured_movies_result, 0);
            while($movie = mysqli_fetch_assoc($featured_movies_result)):
            ?>
            <div class="movie-card" data-category="<?php echo $movie['category_id']; ?>">
                <?php if($movie['premium_only']): ?>
                <div class="premium-badge">
                    <i class="fas fa-crown"></i> Premium
                </div>
                <?php endif; ?>
                <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $movie['poster']; ?>" alt="<?php echo htmlspecialchars($movie['title']); ?>" loading="lazy">
                <?php if(!empty($movie['tags'])): ?>
                <div class="tag-badges">
                    <?php
                    $tag_names = explode(',', $movie['tags']);
                    $displayed_tags = 0;
                    foreach($tag_names as $tag):
                        if($displayed_tags >= 2) break; // Limit to 2 tags
                        $tag_class = 'tag-' . strtolower(str_replace(' ', '-', trim($tag)));
                    ?>
                    <span class="tag-badge <?php echo $tag_class; ?>"><?php echo htmlspecialchars(trim($tag)); ?></span>
                    <?php
                    $displayed_tags++;
                    endforeach; ?>
                </div>
                <?php endif; ?>
                <a href="<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $movie['id']; ?>" class="card-link"></a>
                <div class="movie-card-overlay">
                    <h5 class="movie-card-title"><?php echo htmlspecialchars($movie['title']); ?></h5>
                    <div class="movie-card-info">
                        <span><?php echo $movie['release_year']; ?></span> •
                        <span><?php echo htmlspecialchars($movie['category_name']); ?></span>
                    </div>
                    <div class="movie-card-rating">
                        <i class="fas fa-star"></i> <?php echo number_format($movie['rating'], 1); ?>
                    </div>
                    <a href="<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $movie['id']; ?>" class="btn btn-danger btn-sm mt-2">এখনই দেখুন</a>
                </div>
            </div>
            <?php endwhile; ?>
        </div>
    </div>
</section>

<!-- Featured TV Shows Section -->
<section class="py-5 bg-dark">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="section-title mb-0"><i class="fas fa-tv text-primary me-2"></i>ফিচার্ড টিভি শো</h2>
            <a href="<?php echo SITE_URL; ?>/tvshows.php" class="btn btn-outline-danger">সব দেখুন <i class="fas fa-arrow-right ms-1"></i></a>
        </div>
        <div class="featured-carousel owl-carousel">
            <?php while($tvshow = mysqli_fetch_assoc($featured_tvshows_result)): ?>
            <div class="movie-card" data-category="<?php echo $tvshow['category_id']; ?>">
                <?php if($tvshow['premium_only']): ?>
                <div class="premium-badge">
                    <i class="fas fa-crown"></i> Premium
                </div>
                <?php endif; ?>
                <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $tvshow['poster']; ?>" alt="<?php echo htmlspecialchars($tvshow['title']); ?>" loading="lazy">
                <?php if(!empty($tvshow['tags'])): ?>
                <div class="tag-badges">
                    <?php
                    $tag_names = explode(',', $tvshow['tags']);
                    $displayed_tags = 0;
                    foreach($tag_names as $tag):
                        if($displayed_tags >= 2) break; // Limit to 2 tags
                        $tag_class = 'tag-' . strtolower(str_replace(' ', '-', trim($tag)));
                    ?>
                    <span class="tag-badge <?php echo $tag_class; ?>"><?php echo htmlspecialchars(trim($tag)); ?></span>
                    <?php
                    $displayed_tags++;
                    endforeach; ?>
                </div>
                <?php endif; ?>
                <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $tvshow['id']; ?>" class="card-link"></a>
                <div class="movie-card-overlay">
                    <h5 class="movie-card-title"><?php echo htmlspecialchars($tvshow['title']); ?></h5>
                    <div class="movie-card-info">
                        <span><?php echo $tvshow['start_year']; ?><?php echo $tvshow['end_year'] ? ' - ' . $tvshow['end_year'] : ' - Present'; ?></span> •
                        <span><?php echo htmlspecialchars($tvshow['category_name']); ?></span>
                    </div>
                    <div class="movie-card-rating">
                        <i class="fas fa-star"></i> <?php echo number_format($tvshow['rating'], 1); ?>
                    </div>
                    <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $tvshow['id']; ?>" class="btn btn-danger btn-sm mt-2">এখনই দেখুন</a>
                </div>
            </div>
            <?php endwhile; ?>
        </div>
    </div>
</section>

<!-- Middle Ad Section -->
<?php if (!(isLoggedIn() && isPremium())): ?>
<div class="container my-5">
    <div class="ad-container ad-native">
        <script type='text/javascript' src='//pl27076825.profitableratecpm.com/db/ba/2e/dbba2edda331c47423c9b3fc68f95fb1.js'></script>
    </div>
</div>
<?php endif; ?>

<!-- Latest Movies Section -->
<section class="py-5 latest-section">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="section-title mb-0"><i class="fas fa-clock text-info me-2"></i>সর্বশেষ মুভি</h2>
            <a href="<?php echo SITE_URL; ?>/movies.php" class="btn btn-outline-danger">সব দেখুন <i class="fas fa-arrow-right ms-1"></i></a>
        </div>
        <div class="row">
            <?php while($movie = mysqli_fetch_assoc($latest_movies_result)): ?>
            <div class="col-6 col-md-4 col-lg-2 mb-4">
                <div class="movie-card" data-category="<?php echo $movie['category_id']; ?>">
                    <?php if($movie['premium_only']): ?>
                    <div class="premium-badge">
                        <i class="fas fa-crown"></i> Premium
                    </div>
                    <?php endif; ?>
                    <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $movie['poster']; ?>" alt="<?php echo htmlspecialchars($movie['title']); ?>" loading="lazy">
                    <?php if(!empty($movie['tags'])): ?>
                    <div class="tag-badges">
                        <?php
                        $tag_names = explode(',', $movie['tags']);
                        $displayed_tags = 0;
                        foreach($tag_names as $tag):
                            if($displayed_tags >= 1) break; // Limit to 1 tag for grid layout
                            $tag_class = 'tag-' . strtolower(str_replace(' ', '-', trim($tag)));
                        ?>
                        <span class="tag-badge <?php echo $tag_class; ?>"><?php echo htmlspecialchars(trim($tag)); ?></span>
                        <?php
                        $displayed_tags++;
                        endforeach; ?>
                    </div>
                    <?php endif; ?>
                    <a href="<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $movie['id']; ?>" class="card-link"></a>
                    <div class="movie-card-overlay">
                        <h5 class="movie-card-title"><?php echo htmlspecialchars($movie['title']); ?></h5>
                        <div class="movie-card-info">
                            <span><?php echo $movie['release_year']; ?></span> •
                            <span><?php echo htmlspecialchars($movie['category_name']); ?></span>
                        </div>
                        <div class="movie-card-rating">
                            <i class="fas fa-star"></i> <?php echo number_format($movie['rating'], 1); ?>
                        </div>
                        <a href="<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $movie['id']; ?>" class="btn btn-danger btn-sm mt-2">এখনই দেখুন</a>
                    </div>
                </div>
            </div>
            <?php endwhile; ?>
        </div>
    </div>
</section>

<!-- Latest TV Shows Section -->
<section class="py-5 bg-dark latest-section">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="section-title mb-0"><i class="fas fa-clock text-info me-2"></i>সর্বশেষ টিভি শো</h2>
            <a href="<?php echo SITE_URL; ?>/tvshows.php" class="btn btn-outline-danger">সব দেখুন <i class="fas fa-arrow-right ms-1"></i></a>
        </div>
        <div class="row">
            <?php while($tvshow = mysqli_fetch_assoc($latest_tvshows_result)): ?>
            <div class="col-6 col-md-4 col-lg-2 mb-4">
                <div class="movie-card" data-category="<?php echo $tvshow['category_id']; ?>">
                    <?php if($tvshow['premium_only']): ?>
                    <div class="premium-badge">
                        <i class="fas fa-crown"></i> Premium
                    </div>
                    <?php endif; ?>
                    <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $tvshow['poster']; ?>" alt="<?php echo htmlspecialchars($tvshow['title']); ?>" loading="lazy">
                    <?php if(!empty($tvshow['tags'])): ?>
                    <div class="tag-badges">
                        <?php
                        $tag_names = explode(',', $tvshow['tags']);
                        $displayed_tags = 0;
                        foreach($tag_names as $tag):
                            if($displayed_tags >= 1) break; // Limit to 1 tag for grid layout
                            $tag_class = 'tag-' . strtolower(str_replace(' ', '-', trim($tag)));
                        ?>
                        <span class="tag-badge <?php echo $tag_class; ?>"><?php echo htmlspecialchars(trim($tag)); ?></span>
                        <?php
                        $displayed_tags++;
                        endforeach; ?>
                    </div>
                    <?php endif; ?>
                    <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $tvshow['id']; ?>" class="card-link"></a>
                    <div class="movie-card-overlay">
                        <h5 class="movie-card-title"><?php echo htmlspecialchars($tvshow['title']); ?></h5>
                        <div class="movie-card-info">
                            <span><?php echo $tvshow['start_year']; ?><?php echo $tvshow['end_year'] ? ' - ' . $tvshow['end_year'] : ' - Present'; ?></span> •
                            <span><?php echo htmlspecialchars($tvshow['category_name']); ?></span>
                        </div>
                        <div class="movie-card-rating">
                            <i class="fas fa-star"></i> <?php echo number_format($tvshow['rating'], 1); ?>
                        </div>
                        <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $tvshow['id']; ?>" class="btn btn-danger btn-sm mt-2">এখনই দেখুন</a>
                    </div>
                </div>
            </div>
            <?php endwhile; ?>
        </div>
    </div>
</section>

<!-- Bottom Ad Section -->
<?php if (!(isLoggedIn() && isPremium())): ?>
<div class="container my-5">
    <div class="ad-container ad-banner">
        <script type="text/javascript">
            atOptions = {
                'key' : '735a559a5872816da47237a603cac4ad',
                'format' : 'iframe',
                'height' : 90,
                'width' : 728,
                'params' : {}
            };
        </script>
        <script type="text/javascript" src="//www.highperformanceformat.com/735a559a5872816da47237a603cac4ad/invoke.js"></script>
    </div>
</div>
<?php endif; ?>

<!-- Premium Content Section -->
<?php if(mysqli_num_rows($premium_content_result) > 0): ?>
<section class="py-5 premium-section">
    <div class="container">
        <div class="premium-header mb-4">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="section-title mb-0"><i class="fas fa-crown text-warning me-2"></i>প্রিমিয়াম কন্টেন্ট</h2>
                    <p class="text-muted">প্রিমিয়াম সদস্যদের জন্য এক্সক্লুসিভ কন্টেন্ট</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <a href="<?php echo SITE_URL; ?>/premium.php" class="btn btn-warning btn-lg">
                        <i class="fas fa-crown me-2"></i>প্রিমিয়াম হন
                    </a>
                </div>
            </div>
        </div>
        <div class="row">
            <?php while($content = mysqli_fetch_assoc($premium_content_result)): ?>
            <div class="col-6 col-md-4 col-lg-2 mb-4">
                <div class="movie-card premium-card">
                    <div class="premium-badge">
                        <i class="fas fa-crown"></i> Premium
                    </div>
                    <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $content['poster']; ?>" alt="<?php echo htmlspecialchars($content['title']); ?>" loading="lazy">
                    <?php if(!empty($content['tags'])): ?>
                    <div class="tag-badges">
                        <?php
                        $tag_names = explode(',', $content['tags']);
                        $displayed_tags = 0;
                        foreach($tag_names as $tag):
                            if($displayed_tags >= 1) break; // Limit to 1 tag for grid layout
                            $tag_class = 'tag-' . strtolower(str_replace(' ', '-', trim($tag)));
                        ?>
                        <span class="tag-badge <?php echo $tag_class; ?>"><?php echo htmlspecialchars(trim($tag)); ?></span>
                        <?php
                        $displayed_tags++;
                        endforeach; ?>
                    </div>
                    <?php endif; ?>
                    <?php if($content['content_type'] == 'movie'): ?>
                    <a href="<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $content['id']; ?>" class="card-link"></a>
                    <?php else: ?>
                    <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $content['id']; ?>" class="card-link"></a>
                    <?php endif; ?>
                    <div class="movie-card-overlay">
                        <h5 class="movie-card-title"><?php echo htmlspecialchars($content['title']); ?></h5>
                        <div class="movie-card-info">
                            <span><?php echo $content['release_year'] ?? $content['start_year']; ?></span> •
                            <span><?php echo htmlspecialchars($content['category_name']); ?></span>
                        </div>
                        <div class="movie-card-rating">
                            <i class="fas fa-star"></i> <?php echo number_format($content['rating'], 1); ?>
                        </div>
                        <?php if($content['content_type'] == 'movie'): ?>
                        <a href="<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $content['id']; ?>" class="btn btn-warning btn-sm mt-2">এখনই দেখুন</a>
                        <?php else: ?>
                        <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $content['id']; ?>" class="btn btn-warning btn-sm mt-2">এখনই দেখুন</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endwhile; ?>
        </div>
    </div>
</section>

<!-- Final Ad Section -->
<?php if (!(isLoggedIn() && isPremium())): ?>
<div class="container my-5">
    <div class="row">
        <div class="col-md-6 mb-3">
            <div class="ad-container ad-sidebar">
                <script type="text/javascript">
                    atOptions = {
                        'key' : '7d3b7accac0194a88ccf420c241ec7aa',
                        'format' : 'iframe',
                        'height' : 250,
                        'width' : 300,
                        'params' : {}
                    };
                </script>
                <script type="text/javascript" src="//www.highperformanceformat.com/7d3b7accac0194a88ccf420c241ec7aa/invoke.js"></script>
            </div>
        </div>
        <div class="col-md-6 mb-3">
            <div class="ad-container ad-native">
                <script type='text/javascript' src='//pl27076956.profitableratecpm.com/fd/6c/f8/fd6cf8e64921887d3713ef08ffd94b55.js'></script>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>
<?php endif; ?>

<?php require_once 'includes/footer.php'; ?>