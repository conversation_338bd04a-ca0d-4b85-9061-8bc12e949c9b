/**
 * Download with Pop-under Ad System
 * Shows pop-under ad for 4-5 seconds before starting download
 */

class DownloadPopunder {
    constructor() {
        this.isProcessing = false;
        this.popunderWindow = null;
        this.downloadTimer = null;
        this.countdownTimer = null;
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.bindEvents());
        } else {
            this.bindEvents();
        }
    }

    bindEvents() {
        // Bind to all download buttons
        document.addEventListener('click', (e) => {
            const downloadBtn = e.target.closest('.download-btn, .btn-download, [data-download-url], .download-card, .download-link');
            if (downloadBtn && (downloadBtn.classList.contains('download-btn') || downloadBtn.hasAttribute('data-download-url'))) {
                e.preventDefault();
                e.stopPropagation();
                this.handleDownloadClick(downloadBtn);
            }
        });
    }

    handleDownloadClick(button) {
        if (this.isProcessing) return;

        // Check if user is premium
        if (window.isPremiumUser) {
            // Premium users get direct download
            this.startDirectDownload(button);
            return;
        }

        // Get download URL
        const downloadUrl = this.getDownloadUrl(button);
        if (!downloadUrl) {
            console.error('No download URL found');
            return;
        }

        this.isProcessing = true;
        this.showDownloadModal(downloadUrl, button);
    }

    getDownloadUrl(button) {
        // Try different ways to get download URL
        return button.getAttribute('data-download-url') || 
               button.getAttribute('href') || 
               button.getAttribute('data-href') ||
               button.querySelector('a')?.href;
    }

    showDownloadModal(downloadUrl, button) {
        // Create modal HTML
        const modalHtml = `
            <div class="download-modal-overlay" id="downloadModalOverlay">
                <div class="download-modal">
                    <div class="download-modal-header">
                        <h4><i class="fas fa-download me-2"></i>ডাউনলোড প্রস্তুত করা হচ্ছে...</h4>
                        <button class="download-modal-close" id="downloadModalClose">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="download-modal-body">
                        <div class="download-progress">
                            <div class="download-progress-bar" id="downloadProgressBar"></div>
                        </div>
                        <div class="download-info">
                            <p class="download-message">আপনার ডাউনলোড <span id="downloadCountdown">5</span> সেকেন্ডে শুরু হবে...</p>
                            <div class="download-ads-notice">
                                <i class="fas fa-info-circle me-2"></i>
                                <span>বিনামূল্যে ডাউনলোডের জন্য অনুগ্রহ করে অপেক্ষা করুন</span>
                            </div>
                        </div>
                        <div class="download-premium-offer">
                            <p><i class="fas fa-crown text-warning me-2"></i>প্রিমিয়াম সদস্য হয়ে তাৎক্ষণিক ডাউনলোড করুন!</p>
                            <a href="${window.SITE_URL || ''}/premium.php" class="btn btn-warning btn-sm">
                                <i class="fas fa-crown me-1"></i>প্রিমিয়াম হন
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Get modal elements
        const overlay = document.getElementById('downloadModalOverlay');
        const closeBtn = document.getElementById('downloadModalClose');
        const progressBar = document.getElementById('downloadProgressBar');
        const countdownEl = document.getElementById('downloadCountdown');

        // Show modal
        setTimeout(() => overlay.classList.add('show'), 10);

        // Show pop-under ad
        this.showPopunder();

        // Start countdown
        this.startCountdown(countdownEl, progressBar, () => {
            this.startDownload(downloadUrl);
            this.closeModal(overlay);
        });

        // Close button event
        closeBtn.addEventListener('click', () => {
            this.cancelDownload();
            this.closeModal(overlay);
        });

        // Close on overlay click
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                this.cancelDownload();
                this.closeModal(overlay);
            }
        });
    }

    showPopunder() {
        try {
            // Create pop-under window
            const popunderUrl = 'https://www.highperformanceformat.com/735a559a5872816da47237a603cac4ad/invoke.js';
            
            // Pop-under HTML content
            const popunderContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>CinePix - বিনামূল্যে মুভি ডাউনলোড</title>
                    <meta charset="UTF-8">
                    <style>
                        body { 
                            margin: 0; 
                            padding: 20px; 
                            font-family: Arial, sans-serif; 
                            background: #1a1a1a; 
                            color: white; 
                            text-align: center;
                        }
                        .container { 
                            max-width: 600px; 
                            margin: 50px auto; 
                            padding: 30px; 
                            background: #2d2d2d; 
                            border-radius: 10px; 
                        }
                        .logo { 
                            font-size: 24px; 
                            font-weight: bold; 
                            color: #dc3545; 
                            margin-bottom: 20px; 
                        }
                        .ad-container { 
                            margin: 20px 0; 
                            padding: 20px; 
                            background: #333; 
                            border-radius: 8px; 
                        }
                        .premium-offer {
                            background: linear-gradient(45deg, #ffd700, #ffed4e);
                            color: #000;
                            padding: 20px;
                            border-radius: 8px;
                            margin: 20px 0;
                        }
                        .btn {
                            display: inline-block;
                            padding: 10px 20px;
                            background: #dc3545;
                            color: white;
                            text-decoration: none;
                            border-radius: 5px;
                            margin: 10px;
                        }
                        .btn:hover { background: #c82333; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="logo">CinePix</div>
                        <h2>বিনামূল্যে মুভি ডাউনলোড</h2>
                        <p>আমাদের সাইট থেকে বিনামূল্যে মুভি ডাউনলোড করার জন্য ধন্যবাদ!</p>
                        
                        <div class="ad-container">
                            <script type="text/javascript">
                                atOptions = {
                                    'key' : '735a559a5872816da47237a603cac4ad',
                                    'format' : 'iframe',
                                    'height' : 250,
                                    'width' : 300,
                                    'params' : {}
                                };
                            </script>
                            <script type="text/javascript" src="//www.highperformanceformat.com/735a559a5872816da47237a603cac4ad/invoke.js"></script>
                        </div>

                        <div class="premium-offer">
                            <h3>🎬 প্রিমিয়াম সদস্য হন!</h3>
                            <p>✅ তাৎক্ষণিক ডাউনলোড<br>✅ কোনো বিজ্ঞাপন নেই<br>✅ HD গুণমান<br>✅ একাধিক সার্ভার</p>
                            <a href="${window.SITE_URL || 'https://cinepix.top'}/premium.php" class="btn" target="_parent">
                                প্রিমিয়াম হন - মাত্র ৩০ টাকা/মাস
                            </a>
                        </div>

                        <p><small>এই উইন্ডো ৫ সেকেন্ড পর স্বয়ংক্রিয়ভাবে বন্ধ হয়ে যাবে</small></p>
                    </div>

                    <script>
                        // Auto close after 5 seconds
                        setTimeout(function() {
                            window.close();
                        }, 5000);

                        // Native ads
                        setTimeout(function() {
                            var script1 = document.createElement('script');
                            script1.src = '//pl27076825.profitableratecpm.com/db/ba/2e/dbba2edda331c47423c9b3fc68f95fb1.js';
                            document.body.appendChild(script1);
                        }, 1000);
                    </script>
                </body>
                </html>
            `;

            // Open pop-under
            this.popunderWindow = window.open('', '_blank', 
                'width=800,height=600,scrollbars=yes,resizable=yes,toolbar=no,location=no,directories=no,status=no,menubar=no'
            );
            
            if (this.popunderWindow) {
                this.popunderWindow.document.write(popunderContent);
                this.popunderWindow.document.close();
                
                // Move to background (pop-under effect)
                setTimeout(() => {
                    window.focus();
                    if (window.opener) {
                        window.opener.focus();
                    }
                }, 100);

                // Auto close pop-under after 6 seconds
                setTimeout(() => {
                    if (this.popunderWindow && !this.popunderWindow.closed) {
                        this.popunderWindow.close();
                    }
                }, 6000);
            }
        } catch (error) {
            console.log('Pop-under blocked or failed:', error);
        }
    }

    startCountdown(countdownEl, progressBar, callback) {
        let timeLeft = 5;
        let progress = 0;

        this.countdownTimer = setInterval(() => {
            timeLeft--;
            progress += 20;

            countdownEl.textContent = timeLeft;
            progressBar.style.width = progress + '%';

            if (timeLeft <= 0) {
                clearInterval(this.countdownTimer);
                callback();
            }
        }, 1000);
    }

    startDownload(url) {
        try {
            // Create hidden link and trigger download
            const link = document.createElement('a');
            link.href = url;
            link.download = '';
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Show success message
            this.showSuccessMessage();
        } catch (error) {
            console.error('Download failed:', error);
            // Fallback: open in new tab
            window.open(url, '_blank');
        }
    }

    startDirectDownload(button) {
        const url = this.getDownloadUrl(button);
        if (url) {
            this.startDownload(url);
        }
    }

    showSuccessMessage() {
        // Create success toast
        const toast = document.createElement('div');
        toast.className = 'download-success-toast';
        toast.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            ডাউনলোড শুরু হয়েছে!
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => toast.classList.add('show'), 100);
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => document.body.removeChild(toast), 300);
        }, 3000);
    }

    cancelDownload() {
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
        }
        if (this.downloadTimer) {
            clearTimeout(this.downloadTimer);
        }
        if (this.popunderWindow && !this.popunderWindow.closed) {
            this.popunderWindow.close();
        }
        this.isProcessing = false;
    }

    closeModal(overlay) {
        overlay.classList.remove('show');
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        }, 300);
        this.isProcessing = false;
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new DownloadPopunder();
});

// Also initialize immediately if DOM is already ready
if (document.readyState !== 'loading') {
    new DownloadPopunder();
}
