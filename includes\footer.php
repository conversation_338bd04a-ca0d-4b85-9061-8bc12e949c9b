    </main>
    <!-- Footer -->
    <footer class="footer mt-5 py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4">
                    <h5 class="text-danger mb-3">Cine<span class="text-light">Pix</span></h5>
                    <p class="text-muted">The best platform to watch your favorite movies and TV shows anytime, anywhere. Enjoy unlimited streaming of high-quality content.</p>
                    <div class="social-icons">
                        <a href="#" class="social-icon facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-icon twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-icon instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-icon youtube"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="col-md-2 mb-4">
                    <h6 class="text-white mb-3">Navigation</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="<?php echo SITE_URL; ?>" class="text-muted text-decoration-none">Home</a></li>
                        <li class="mb-2"><a href="<?php echo SITE_URL; ?>/movies.php" class="text-muted text-decoration-none">Movies</a></li>
                        <li class="mb-2"><a href="<?php echo SITE_URL; ?>/tvshows.php" class="text-muted text-decoration-none">TV Shows</a></li>
                        <li class="mb-2"><a href="<?php echo SITE_URL; ?>/watchlist.php" class="text-muted text-decoration-none">My List</a></li>
                    </ul>
                </div>
                <div class="col-md-2 mb-4">
                    <h6 class="text-white mb-3">Categories</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="<?php echo SITE_URL; ?>/movies.php?category=1" class="text-muted text-decoration-none">Action</a></li>
                        <li class="mb-2"><a href="<?php echo SITE_URL; ?>/movies.php?category=2" class="text-muted text-decoration-none">Comedy</a></li>
                        <li class="mb-2"><a href="<?php echo SITE_URL; ?>/movies.php?category=3" class="text-muted text-decoration-none">Drama</a></li>
                        <li class="mb-2"><a href="<?php echo SITE_URL; ?>/movies.php?category=5" class="text-muted text-decoration-none">Sci-Fi</a></li>
                    </ul>
                </div>
                <div class="col-md-2 mb-4">
                    <h6 class="text-white mb-3">মোবাইল অ্যাপ</h6>
                    <div class="app-download-footer">
                        <a href="#" id="footerAppBtn" class="btn btn-outline-danger mb-2">
                            <i class="fab fa-android me-2"></i> অ্যাপ ডাউনলোড করুন
                        </a>
                        <p class="text-muted small">আমাদের অফিসিয়াল অ্যাপ ডাউনলোড করে আরও সহজে মুভি এবং টিভি সিরিজ দেখুন।</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <h6 class="text-white mb-3">Subscribe to Newsletter</h6>
                    <p class="text-muted mb-3">Get updates on new releases and exclusive offers</p>
                    <form class="d-flex">
                        <input type="email" class="form-control me-2" placeholder="Your email">
                        <button class="btn btn-danger" type="submit">Subscribe</button>
                    </form>
                </div>
            </div>
            <hr class="border-secondary mt-4">
            <div class="row">
                <div class="col-md-6 text-center text-md-start">
                    <p class="text-muted mb-0">&copy; <?php echo date('Y'); ?> Cinepix. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <ul class="list-inline mb-0">
                        <li class="list-inline-item"><a href="#" class="text-muted text-decoration-none">Privacy Policy</a></li>
                        <li class="list-inline-item"><span class="text-muted">|</span></li>
                        <li class="list-inline-item"><a href="#" class="text-muted text-decoration-none">Terms of Service</a></li>
                        <li class="list-inline-item"><span class="text-muted">|</span></li>
                        <li class="list-inline-item"><a href="#" class="text-muted text-decoration-none">FAQ</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>

    <!-- Floating App Download Button -->
    <div class="floating-app-icon" id="appDownloadIcon" style="right: 30px !important; bottom: 100px !important;">
        <i class="fab fa-android"></i>
    </div>

    <!-- Floating Chat Icon and Container -->
    <?php if (isLoggedIn()): ?>
    <?php
    // Check for unread messages
    $user_id = $_SESSION['user_id'];
    $unread_query = "SELECT COUNT(*) as unread_count FROM chat_messages cm
                    JOIN chat_sessions cs ON cm.session_id = cs.id
                    WHERE cs.user_id = $user_id AND cm.receiver_id = $user_id AND cm.is_read = FALSE";
    $unread_result = mysqli_query($conn, $unread_query);
    $unread_count = 0;
    if ($unread_result && mysqli_num_rows($unread_result) > 0) {
        $unread_data = mysqli_fetch_assoc($unread_result);
        $unread_count = $unread_data['unread_count'];
    }
    ?>
    <div class="floating-chat-icon<?php echo $unread_count > 0 ? ' has-unread' : ''; ?>" id="chatIcon" style="right: 30px !important; left: auto !important;">
        <i class="fas fa-comments"></i>
        <?php if ($unread_count > 0): ?>
        <span class="chat-pulse" id="chatPulse"><?php echo $unread_count; ?></span>
        <?php endif; ?>
    </div>

    <div class="chat-container" id="chatContainer" style="display: none; right: 30px !important; left: auto !important;">
        <div class="chat-header">
            <h5 class="chat-title">লাইভ চ্যাট</h5>
            <button type="button" class="chat-close" id="closeChatBtn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="chat-messages" id="chatMessages">
            <div class="chat-welcome">
                <h4>স্বাগতম!</h4>
                <p>আমাদের সাথে লাইভ চ্যাট করুন। আমরা আপনাকে সাহায্য করতে সবসময় প্রস্তুত।</p>
            </div>
        </div>
        <div class="chat-input-container">
            <form id="chatForm">
                <div class="d-flex">
                    <textarea class="chat-input" id="chatInput" placeholder="আপনার মেসেজ লিখুন..." rows="1"></textarea>
                    <button type="submit" class="chat-send-btn" id="chatSendBtn">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>
    <?php endif; ?>

    <script>
    // Chat functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Elements
        const chatIcon = document.getElementById('chatIcon');
        const chatContainer = document.getElementById('chatContainer');
        const closeChatBtn = document.getElementById('closeChatBtn');
        const chatMessages = document.getElementById('chatMessages');
        const chatInput = document.getElementById('chatInput');
        const chatSendBtn = document.getElementById('chatSendBtn');
        const chatPulse = document.getElementById('chatPulse');

        // Variables
        let chatSession = null;
        let lastMessageId = 0;
        let pollingInterval = null;
        let chatIsOpen = false;

        // Show chat icon
        if (chatIcon) {
            chatIcon.style.display = 'flex';
        }

        // Toggle chat container
        if (chatIcon && chatContainer) {
            chatIcon.addEventListener('click', function() {
                if (chatIsOpen) {
                    // If chat is already open, close it
                    chatContainer.style.display = 'none';
                    chatIcon.style.display = 'flex';
                    chatIsOpen = false;
                    stopPolling();
                } else {
                    // Open the chat
                    chatContainer.style.display = 'flex';
                    chatIcon.style.display = 'none';
                    chatIsOpen = true;
                    initChat();

                    // Remove unread indicator
                    if (chatPulse) {
                        chatPulse.style.display = 'none';
                    }
                    if (chatIcon.classList.contains('has-unread')) {
                        chatIcon.classList.remove('has-unread');
                    }
                }
            });
        }

        // Close chat
        if (closeChatBtn && chatContainer) {
            closeChatBtn.addEventListener('click', function() {
                chatContainer.style.display = 'none';
                chatIcon.style.display = 'flex';
                chatIsOpen = false;
                stopPolling();
            });
        }

        // Close chat when clicking outside
        document.addEventListener('click', function(e) {
            if (chatIsOpen && chatContainer && chatIcon) {
                // If click is outside chat container and not on chat icon
                if (!chatContainer.contains(e.target) && !chatIcon.contains(e.target)) {
                    chatContainer.style.display = 'none';
                    chatIcon.style.display = 'flex';
                    chatIsOpen = false;
                    stopPolling();
                }
            }
        });

        // Initialize chat
        function initChat() {
            // Clear existing messages to prevent duplicates
            chatMessages.innerHTML = '';
            // Add welcome message back
            chatMessages.innerHTML = `
                <div class="chat-welcome">
                    <h4>স্বাগতম!</h4>
                    <p>আমাদের সাথে লাইভ চ্যাট করুন। আমরা আপনাকে সাহায্য করতে সবসময় প্রস্তুত।</p>
                </div>
            `;

            // Reset variables
            lastMessageId = 0;

            fetch('<?php echo SITE_URL; ?>/chat_server.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=init'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    chatSession = data.session;
                    if (data.messages && data.messages.length > 0) {
                        // Remove welcome message if we have actual messages
                        chatMessages.innerHTML = '';
                        renderMessages(data.messages);
                    }
                    startPolling();
                }
            })
            .catch(error => {
                console.error('Error initializing chat:', error);
            });
        }

        // Send message
        function sendMessage() {
            const message = chatInput.value.trim();
            if (!message || !chatSession) return;

            // Find an admin to send to (default to admin ID 1 if none found)
            const adminId = 1; // Default admin ID

            const formData = new FormData();
            formData.append('action', 'send');
            formData.append('session_id', chatSession.id);
            formData.append('message', message);
            formData.append('receiver_id', adminId);

            fetch('<?php echo SITE_URL; ?>/chat_server.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    chatInput.value = '';
                    addMessage(data.message);
                }
            })
            .catch(error => {
                console.error('Error sending message:', error);
            });
        }

        // Poll for new messages
        function pollMessages() {
            if (!chatSession) return;

            const formData = new FormData();
            formData.append('action', 'poll');
            formData.append('session_id', chatSession.id);
            formData.append('last_message_id', lastMessageId);

            fetch('<?php echo SITE_URL; ?>/chat_server.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.messages.length > 0) {
                    renderMessages(data.messages);

                    // Check for unread messages from admin
                    const hasUnreadFromAdmin = data.messages.some(msg =>
                        msg.sender_role === 'admin' && !msg.is_read);

                    // Show notification if chat is not open
                    if (hasUnreadFromAdmin && !chatIsOpen) {
                        // Count unread messages
                        const unreadCount = data.messages.filter(msg =>
                            msg.sender_role === 'admin' && !msg.is_read).length;

                        // Update the chat pulse with count
                        if (chatPulse) {
                            chatPulse.style.display = 'block';
                            chatPulse.textContent = unreadCount;
                        }

                        if (chatIcon && !chatIcon.classList.contains('has-unread')) {
                            chatIcon.classList.add('has-unread');
                        }

                        // Refresh the page to update the PHP-generated unread count
                        // This is needed because our PHP code checks the database for unread messages
                        // We could alternatively use AJAX to update just the count
                        if (unreadCount > 0) {
                            // Use a cookie to prevent infinite refresh
                            if (!document.cookie.includes('chat_refreshed=true')) {
                                document.cookie = 'chat_refreshed=true; max-age=5;'; // 5 seconds expiry
                                location.reload();
                            }
                        }
                    }
                }
            })
            .catch(error => {
                console.error('Error polling messages:', error);
            });
        }

        // Start polling
        function startPolling() {
            stopPolling(); // Clear any existing interval
            pollingInterval = setInterval(pollMessages, 3000); // Poll every 3 seconds
        }

        // Stop polling
        function stopPolling() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
                pollingInterval = null;
            }
        }

        // Render messages
        function renderMessages(messages) {
            if (!messages || messages.length === 0) return;

            // Check for duplicates before adding
            messages.forEach(message => {
                // Check if message already exists in the DOM
                const existingMessage = document.querySelector(`.chat-message[data-message-id="${message.id}"]`);
                if (!existingMessage) {
                    addMessage(message);
                }
            });

            // Update last message ID
            const lastMessage = messages[messages.length - 1];
            if (lastMessage && lastMessage.id > lastMessageId) {
                lastMessageId = lastMessage.id;
            }

            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Add a single message to the chat
        function addMessage(message) {
            const messageElement = document.createElement('div');
            messageElement.classList.add('chat-message');
            messageElement.classList.add(message.is_self ? 'user' : 'admin');
            // Add data attribute for message ID to prevent duplicates
            messageElement.setAttribute('data-message-id', message.id);

            const messageContent = document.createElement('div');
            messageContent.classList.add('chat-message-content');
            messageContent.textContent = message.message;

            const messageTime = document.createElement('div');
            messageTime.classList.add('chat-message-time');
            messageTime.textContent = formatTime(message.created_at);

            messageElement.appendChild(messageContent);
            messageElement.appendChild(messageTime);

            if (message.is_self) {
                const messageStatus = document.createElement('div');
                messageStatus.classList.add('chat-message-status');
                messageStatus.classList.add(message.is_read ? 'read' : '');
                messageStatus.textContent = message.is_read ? 'পঠিত' : 'পাঠানো হয়েছে';
                messageElement.appendChild(messageStatus);
            }

            chatMessages.appendChild(messageElement);

            // Update last message ID
            if (message.id > lastMessageId) {
                lastMessageId = message.id;
            }

            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Format time
        function formatTime(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        }

        // Send message on button click
        if (chatSendBtn) {
            chatSendBtn.addEventListener('click', sendMessage);
        }

        // Send message on Enter key
        if (chatInput) {
            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Auto-resize textarea
            chatInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
            });
        }
    });
    </script>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Owl Carousel -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo SITE_URL; ?>/js/main.js"></script>
    <script src="<?php echo SITE_URL; ?>/js/card-improvements.js"></script>
    <script src="<?php echo SITE_URL; ?>/js/pwa-app.js"></script>
    <?php
    // Add page-specific JS files
    $current_page = basename($_SERVER['PHP_SELF']);
    if ($current_page === 'profile.php') {
        echo '<script src="' . SITE_URL . '/js/profile.js"></script>';
    }
    ?>

    <!-- App Download Modal -->
    <div class="modal fade" id="appDownloadModal" tabindex="-1" aria-labelledby="appDownloadModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content bg-dark text-white">
                <div class="modal-header border-secondary">
                    <h5 class="modal-title" id="appDownloadModalLabel">সিনেপিক্স অ্যাপ ডাউনলোড করুন</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img src="<?php echo SITE_URL; ?>/assets/images/app_qr.png" alt="App Download QR Code" class="img-fluid mb-3" style="max-width: 200px;">
                    <h4 class="mb-3">সিনেপিক্স অফিসিয়াল অ্যাপ</h4>
                    <p>আমাদের অফিসিয়াল অ্যাপ ডাউনলোড করে আরও সহজে মুভি এবং টিভি সিরিজ দেখুন। অ্যাপে রয়েছে:</p>
                    <ul class="list-unstyled text-start">
                        <li><i class="fas fa-check text-success me-2"></i> অফলাইনে দেখার সুবিধা</li>
                        <li><i class="fas fa-check text-success me-2"></i> মাল্টি-অডিও সাপোর্ট</li>
                        <li><i class="fas fa-check text-success me-2"></i> টিভি রিমোট সাপোর্ট</li>
                        <li><i class="fas fa-check text-success me-2"></i> উন্নত ভিডিও প্লেয়ার</li>
                        <li><i class="fas fa-check text-success me-2"></i> ফাস্ট স্ট্রিমিং</li>
                    </ul>
                    <div class="mt-4">
                        <a href="#" class="btn btn-success btn-lg">
                            <i class="fas fa-download me-2"></i> ডাউনলোড করুন
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- App Download CSS -->
    <style>
        .floating-app-icon {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #28a745;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            z-index: 999;
            transition: all 0.3s ease;
        }

        .floating-app-icon:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
        }

        .floating-app-icon i {
            font-size: 24px;
        }

        .app-download-footer {
            display: flex;
            flex-direction: column;
        }
    </style>

    <!-- App Download Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // App download modal
            const appDownloadModal = new bootstrap.Modal(document.getElementById('appDownloadModal'));

            // Floating app icon
            const appDownloadIcon = document.getElementById('appDownloadIcon');
            if (appDownloadIcon) {
                appDownloadIcon.addEventListener('click', function() {
                    appDownloadModal.show();
                });
            }

            // Footer app button
            const footerAppBtn = document.getElementById('footerAppBtn');
            if (footerAppBtn) {
                footerAppBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    appDownloadModal.show();
                });
            }

            // Premium page app button
            const downloadAppBtn = document.getElementById('downloadAppBtn');
            if (downloadAppBtn) {
                downloadAppBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    appDownloadModal.show();
                });
            }

            // Nav menu app button
            const navAppDownload = document.getElementById('navAppDownload');
            if (navAppDownload) {
                navAppDownload.addEventListener('click', function(e) {
                    e.preventDefault();
                    appDownloadModal.show();
                });
            }
        });
    </script>

    <!-- Ad Enhancements Script -->
    <script src="<?php echo SITE_URL; ?>/js/ad-enhancements.js?v=<?php echo time(); ?>"></script>

    <!-- Debug Script -->
    <script>
        console.log('Footer script loaded');
        // Set premium user status and site URL for JavaScript
        window.isPremiumUser = <?php echo (isLoggedIn() && isPremium()) ? 'true' : 'false'; ?>;
        window.SITE_URL = '<?php echo SITE_URL; ?>';
        console.log('Premium user:', window.isPremiumUser);
        console.log('Site URL:', window.SITE_URL);
    </script>



    <!-- Enhanced Homepage Slider Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Enhanced Slider Functionality
            const slides = document.querySelectorAll('.slide');
            const dots = document.querySelectorAll('.slider-dot');
            const prevBtn = document.querySelector('.slider-prev');
            const nextBtn = document.querySelector('.slider-next');
            let currentSlide = 0;
            let slideInterval;

            function showSlide(index) {
                // Remove active class from all slides and dots
                slides.forEach(slide => slide.classList.remove('active'));
                dots.forEach(dot => dot.classList.remove('active'));

                // Add active class to current slide and dot
                if (slides[index]) {
                    slides[index].classList.add('active');
                }
                if (dots[index]) {
                    dots[index].classList.add('active');
                }

                currentSlide = index;
            }

            function nextSlide() {
                const next = (currentSlide + 1) % slides.length;
                showSlide(next);
            }

            function prevSlide() {
                const prev = (currentSlide - 1 + slides.length) % slides.length;
                showSlide(prev);
            }

            function startAutoSlide() {
                slideInterval = setInterval(nextSlide, 6000); // Change slide every 6 seconds
            }

            function stopAutoSlide() {
                clearInterval(slideInterval);
            }

            // Event listeners
            if (nextBtn) {
                nextBtn.addEventListener('click', () => {
                    nextSlide();
                    stopAutoSlide();
                    startAutoSlide(); // Restart auto-slide
                });
            }

            if (prevBtn) {
                prevBtn.addEventListener('click', () => {
                    prevSlide();
                    stopAutoSlide();
                    startAutoSlide(); // Restart auto-slide
                });
            }

            // Dot navigation
            dots.forEach((dot, index) => {
                dot.addEventListener('click', () => {
                    showSlide(index);
                    stopAutoSlide();
                    startAutoSlide(); // Restart auto-slide
                });
            });

            // Pause on hover
            const sliderWrapper = document.querySelector('.slider-wrapper');
            if (sliderWrapper) {
                sliderWrapper.addEventListener('mouseenter', stopAutoSlide);
                sliderWrapper.addEventListener('mouseleave', startAutoSlide);
            }

            // Initialize slider
            if (slides.length > 0) {
                showSlide(0);
                startAutoSlide();
            }

            // Enhanced Owl Carousel initialization
            if (typeof $.fn.owlCarousel !== 'undefined') {
                $('.featured-carousel').owlCarousel({
                    loop: true,
                    margin: 20,
                    nav: true,
                    dots: false,
                    autoplay: true,
                    autoplayTimeout: 4000,
                    autoplayHoverPause: true,
                    navText: ['<i class="fas fa-chevron-left"></i>', '<i class="fas fa-chevron-right"></i>'],
                    responsive: {
                        0: { items: 2 },
                        576: { items: 3 },
                        768: { items: 4 },
                        992: { items: 5 },
                        1200: { items: 6 }
                    }
                });
            }

            // Smooth scrolling for horizontal carousels
            const carousels = document.querySelectorAll('.top-10-carousel');
            carousels.forEach(carousel => {
                let isDown = false;
                let startX;
                let scrollLeft;

                carousel.addEventListener('mousedown', (e) => {
                    isDown = true;
                    carousel.classList.add('active');
                    startX = e.pageX - carousel.offsetLeft;
                    scrollLeft = carousel.scrollLeft;
                });

                carousel.addEventListener('mouseleave', () => {
                    isDown = false;
                    carousel.classList.remove('active');
                });

                carousel.addEventListener('mouseup', () => {
                    isDown = false;
                    carousel.classList.remove('active');
                });

                carousel.addEventListener('mousemove', (e) => {
                    if (!isDown) return;
                    e.preventDefault();
                    const x = e.pageX - carousel.offsetLeft;
                    const walk = (x - startX) * 2;
                    carousel.scrollLeft = scrollLeft - walk;
                });
            });

            // Enhanced movie card hover effects
            const movieCards = document.querySelectorAll('.movie-card');
            movieCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>

    <!-- PWA Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('<?php echo SITE_URL; ?>/service-worker.js')
                    .then(function(registration) {
                        console.log('Service Worker registered with scope:', registration.scope);
                    })
                    .catch(function(error) {
                        console.log('Service Worker registration failed:', error);
                    });
            });
        }
    </script>
</body>
</html>
<?php
// Flush the output buffer and send the content to the browser
if (ob_get_level()) {
    ob_end_flush();
}
?>