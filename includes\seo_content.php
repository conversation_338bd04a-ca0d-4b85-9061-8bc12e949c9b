<?php
/**
 * SEO Content Generator for CinePix
 * Generates SEO-optimized content for better search rankings
 */

/**
 * Generate SEO content for movie details page
 */
function generateMovieSeoContent($movie) {
    $content = [];
    
    // Main description with keywords
    $content['description'] = "Download " . $movie['title'] . " (" . $movie['release_year'] . ") full movie in HD quality. " . 
                             "Watch " . $movie['title'] . " online for free. " . 
                             substr($movie['description'], 0, 150) . "...";
    
    // Keywords
    $content['keywords'] = [
        $movie['title'] . " download",
        $movie['title'] . " full movie",
        $movie['title'] . " " . $movie['release_year'],
        $movie['title'] . " HD download",
        $movie['title'] . " 720p",
        $movie['title'] . " 1080p",
        $movie['title'] . " watch online",
        $movie['title'] . " free download",
        $movie['category_name'] . " movie",
        "Bengali movie download",
        "HD movie download",
        "free movie streaming"
    ];
    
    // Related content
    $content['related_searches'] = [
        $movie['title'] . " full movie download",
        $movie['title'] . " HD quality",
        $movie['title'] . " Bengali dubbed",
        $movie['title'] . " English subtitles",
        $movie['title'] . " trailer",
        $movie['title'] . " review",
        $movie['title'] . " cast",
        $movie['title'] . " songs"
    ];
    
    // FAQ content
    $content['faq'] = [
        [
            'question' => "How to download " . $movie['title'] . " full movie?",
            'answer' => "You can download " . $movie['title'] . " (" . $movie['release_year'] . ") full movie from our website in various qualities including 720p, 1080p, and HD. Simply click on the download links provided below."
        ],
        [
            'question' => "Is " . $movie['title'] . " available for free download?",
            'answer' => "Yes, " . $movie['title'] . " is available for free download on our platform. You can watch it online or download it in your preferred quality."
        ],
        [
            'question' => "What quality options are available for " . $movie['title'] . "?",
            'answer' => $movie['title'] . " is available in multiple quality options including 480p, 720p, 1080p, and HD quality to suit your device and internet speed."
        ],
        [
            'question' => "Can I watch " . $movie['title'] . " with subtitles?",
            'answer' => "Yes, " . $movie['title'] . " is available with both Bengali and English subtitles for better viewing experience."
        ]
    ];
    
    return $content;
}

/**
 * Generate SEO content for TV show details page
 */
function generateTvShowSeoContent($tvshow) {
    $content = [];
    
    // Main description with keywords
    $content['description'] = "Watch " . $tvshow['title'] . " (" . $tvshow['release_year'] . ") TV series online. " . 
                             "Stream all episodes of " . $tvshow['title'] . " in HD quality. " . 
                             substr($tvshow['description'], 0, 150) . "...";
    
    // Keywords
    $content['keywords'] = [
        $tvshow['title'] . " watch online",
        $tvshow['title'] . " TV series",
        $tvshow['title'] . " episodes",
        $tvshow['title'] . " " . $tvshow['release_year'],
        $tvshow['title'] . " streaming",
        $tvshow['title'] . " all episodes",
        $tvshow['title'] . " HD quality",
        $tvshow['category_name'] . " series",
        "Bengali TV series",
        "online series streaming",
        "free TV shows"
    ];
    
    // Related content
    $content['related_searches'] = [
        $tvshow['title'] . " all episodes",
        $tvshow['title'] . " season 1",
        $tvshow['title'] . " latest episode",
        $tvshow['title'] . " Bengali dubbed",
        $tvshow['title'] . " English subtitles",
        $tvshow['title'] . " review",
        $tvshow['title'] . " cast",
        $tvshow['title'] . " trailer"
    ];
    
    // FAQ content
    $content['faq'] = [
        [
            'question' => "How to watch " . $tvshow['title'] . " online?",
            'answer' => "You can watch " . $tvshow['title'] . " (" . $tvshow['release_year'] . ") TV series online on our platform. All episodes are available in HD quality for free streaming."
        ],
        [
            'question' => "How many episodes are there in " . $tvshow['title'] . "?",
            'answer' => $tvshow['title'] . " has multiple episodes across different seasons. Check our episode list for complete details of all available episodes."
        ],
        [
            'question' => "Is " . $tvshow['title'] . " available with subtitles?",
            'answer' => "Yes, " . $tvshow['title'] . " is available with both Bengali and English subtitles for better understanding and viewing experience."
        ]
    ];
    
    return $content;
}

/**
 * Generate internal linking suggestions
 */
function generateInternalLinks($content_type, $category_id, $current_id) {
    global $conn;
    
    $links = [];
    
    if ($content_type === 'movie') {
        // Related movies from same category
        $query = "SELECT id, title, release_year, poster FROM movies 
                 WHERE category_id = $category_id AND id != $current_id AND is_active = 1 
                 ORDER BY rating DESC LIMIT 6";
        $result = mysqli_query($conn, $query);
        
        if ($result && mysqli_num_rows($result) > 0) {
            while ($row = mysqli_fetch_assoc($result)) {
                $links[] = [
                    'title' => $row['title'] . " (" . $row['release_year'] . ")",
                    'url' => getMovieSeoUrl($row['id'], $row['title'], $row['release_year']),
                    'type' => 'related_movie'
                ];
            }
        }
        
        // Popular movies
        $popular_query = "SELECT id, title, release_year FROM movies 
                         WHERE is_active = 1 AND id != $current_id 
                         ORDER BY rating DESC LIMIT 5";
        $popular_result = mysqli_query($conn, $popular_query);
        
        if ($popular_result && mysqli_num_rows($popular_result) > 0) {
            while ($row = mysqli_fetch_assoc($popular_result)) {
                $links[] = [
                    'title' => $row['title'] . " Download",
                    'url' => getMovieDownloadUrl($row['id'], $row['title'], $row['release_year']),
                    'type' => 'popular_movie'
                ];
            }
        }
        
    } else if ($content_type === 'tvshow') {
        // Related TV shows from same category
        $query = "SELECT id, title, release_year FROM tvshows 
                 WHERE category_id = $category_id AND id != $current_id AND is_active = 1 
                 ORDER BY rating DESC LIMIT 6";
        $result = mysqli_query($conn, $query);
        
        if ($result && mysqli_num_rows($result) > 0) {
            while ($row = mysqli_fetch_assoc($result)) {
                $links[] = [
                    'title' => $row['title'] . " (" . $row['release_year'] . ")",
                    'url' => getTvShowSeoUrl($row['id'], $row['title'], $row['release_year']),
                    'type' => 'related_tvshow'
                ];
            }
        }
    }
    
    return $links;
}

/**
 * Generate breadcrumb JSON-LD
 */
function generateBreadcrumbSchema($breadcrumb) {
    $items = [];
    $position = 1;
    
    foreach ($breadcrumb as $item) {
        $items[] = [
            "@type" => "ListItem",
            "position" => $position,
            "name" => $item['name'],
            "item" => $item['url'] ? $item['url'] : null
        ];
        $position++;
    }
    
    return [
        "@context" => "https://schema.org",
        "@type" => "BreadcrumbList",
        "itemListElement" => $items
    ];
}

/**
 * Generate FAQ JSON-LD schema
 */
function generateFAQJsonLD($faqs) {
    $faq_items = [];
    
    foreach ($faqs as $faq) {
        $faq_items[] = [
            "@type" => "Question",
            "name" => $faq['question'],
            "acceptedAnswer" => [
                "@type" => "Answer",
                "text" => $faq['answer']
            ]
        ];
    }
    
    return [
        "@context" => "https://schema.org",
        "@type" => "FAQPage",
        "mainEntity" => $faq_items
    ];
}

/**
 * Generate popular search terms for homepage
 */
function getPopularSearchTerms() {
    return [
        "Avatar full movie download",
        "Avengers Endgame Bengali dubbed",
        "Spider-Man No Way Home HD",
        "Batman 2022 download",
        "Top Bengali movies 2024",
        "Hollywood movies Bengali dubbed",
        "Latest Bollywood movies",
        "Action movies HD download",
        "Comedy movies online",
        "Thriller series watch online",
        "Bengali web series",
        "Hindi dubbed movies",
        "English movies with subtitles",
        "4K movies download",
        "HD TV series streaming"
    ];
}

/**
 * Generate category-specific content
 */
function generateCategoryContent($category_name, $type = 'movie') {
    $content = [];
    
    $content['title'] = "Download Latest " . $category_name . " " . ucfirst($type) . "s | CinePix";
    $content['description'] = "Download latest " . strtolower($category_name) . " " . $type . "s in HD quality. " .
                             "Watch and download " . strtolower($category_name) . " " . $type . "s online for free.";
    
    $content['keywords'] = [
        strtolower($category_name) . " " . $type . "s",
        strtolower($category_name) . " " . $type . " download",
        "latest " . strtolower($category_name) . " " . $type . "s",
        "HD " . strtolower($category_name) . " " . $type . "s",
        "free " . strtolower($category_name) . " " . $type . "s",
        "Bengali " . strtolower($category_name) . " " . $type . "s"
    ];
    
    return $content;
}
?>
