<?php
require_once 'includes/config.php';
require_once 'includes/seo_helper.php';
require_once 'includes/seo_content.php';

// Test movie data
$test_movie = [
    'id' => 1,
    'title' => 'Avatar',
    'release_year' => 2009,
    'description' => 'A paraplegic Marine dispatched to the moon Pandora on a unique mission becomes torn between following his orders and protecting the world he feels is his home.',
    'category_name' => 'Action',
    'category_id' => 1,
    'poster' => 'avatar.jpg'
];

// Test TV show data
$test_tvshow = [
    'id' => 1,
    'title' => 'Breaking Bad',
    'release_year' => 2008,
    'description' => 'A high school chemistry teacher diagnosed with inoperable lung cancer turns to manufacturing and selling methamphetamine.',
    'category_name' => 'Drama',
    'category_id' => 2,
    'poster' => 'breaking-bad.jpg'
];

echo "<h1>SEO Test Results</h1>";

echo "<h2>Movie SEO Test</h2>";
echo "<h3>SEO URL:</h3>";
echo getMovieSeoUrl($test_movie['id'], $test_movie['title'], $test_movie['release_year']) . "<br><br>";

echo "<h3>Keywords:</h3>";
echo getMovieKeywords($test_movie) . "<br><br>";

echo "<h3>Breadcrumb:</h3>";
$movie_breadcrumb = getMovieBreadcrumb($test_movie);
foreach ($movie_breadcrumb as $crumb) {
    if ($crumb['url']) {
        echo "<a href='" . $crumb['url'] . "'>" . $crumb['name'] . "</a> > ";
    } else {
        echo $crumb['name'];
    }
}
echo "<br><br>";

echo "<h3>SEO Content:</h3>";
$movie_seo_content = generateMovieSeoContent($test_movie);
echo "<strong>Description:</strong> " . $movie_seo_content['description'] . "<br>";
echo "<strong>Keywords:</strong> " . implode(", ", $movie_seo_content['keywords']) . "<br><br>";

echo "<h2>TV Show SEO Test</h2>";
echo "<h3>SEO URL:</h3>";
echo getTvShowSeoUrl($test_tvshow['id'], $test_tvshow['title'], $test_tvshow['release_year']) . "<br><br>";

echo "<h3>Keywords:</h3>";
echo getTvShowKeywords($test_tvshow) . "<br><br>";

echo "<h3>Breadcrumb:</h3>";
$tvshow_breadcrumb = getTvShowBreadcrumb($test_tvshow);
foreach ($tvshow_breadcrumb as $crumb) {
    if ($crumb['url']) {
        echo "<a href='" . $crumb['url'] . "'>" . $crumb['name'] . "</a> > ";
    } else {
        echo $crumb['name'];
    }
}
echo "<br><br>";

echo "<h3>SEO Content:</h3>";
$tvshow_seo_content = generateTvShowSeoContent($test_tvshow);
echo "<strong>Description:</strong> " . $tvshow_seo_content['description'] . "<br>";
echo "<strong>Keywords:</strong> " . implode(", ", $tvshow_seo_content['keywords']) . "<br><br>";

echo "<h2>URL Generation Test</h2>";
echo "<h3>Category URLs:</h3>";
echo "Action Movies: " . getCategorySeoUrl(1, 'Action') . "<br>";
echo "Drama Series: " . getCategorySeoUrl(2, 'Drama') . "<br><br>";

echo "<h3>Search URLs:</h3>";
echo "Search Avatar: " . getSearchSeoUrl('Avatar') . "<br>";
echo "Search Action: " . getSearchSeoUrl('Action') . "<br><br>";

echo "<h3>Download URLs:</h3>";
echo "Download Avatar: " . getMovieDownloadUrl($test_movie['id'], $test_movie['title'], $test_movie['release_year']) . "<br>";
echo "Watch Avatar: " . getMovieWatchUrl($test_movie['id'], $test_movie['title'], $test_movie['release_year']) . "<br><br>";

echo "<h2>Structured Data Test</h2>";
echo "<h3>FAQ Schema:</h3>";
$faq_schema = generateFAQJsonLD($movie_seo_content['faq']);
echo "<pre>" . json_encode($faq_schema, JSON_PRETTY_PRINT) . "</pre>";

echo "<h3>Breadcrumb Schema:</h3>";
$breadcrumb_schema = generateBreadcrumbSchema($movie_breadcrumb);
echo "<pre>" . json_encode($breadcrumb_schema, JSON_PRETTY_PRINT) . "</pre>";

echo "<h2>Popular Search Terms</h2>";
$popular_terms = getPopularSearchTerms();
foreach ($popular_terms as $term) {
    echo "- " . $term . "<br>";
}

echo "<br><h2>✅ All SEO Functions Working Properly!</h2>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}

h1, h2, h3 {
    color: #333;
}

pre {
    background-color: #f8f8f8;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    overflow-x: auto;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
