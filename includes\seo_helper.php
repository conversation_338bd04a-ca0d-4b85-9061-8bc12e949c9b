<?php
/**
 * SEO Helper Functions for CinePix
 * Generates SEO-friendly URLs and handles SEO-related functionality
 */

/**
 * Generate SEO-friendly slug from title
 */
function generateSlug($title) {
    // Convert to lowercase
    $slug = strtolower($title);
    
    // Replace spaces and special characters with hyphens
    $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
    $slug = preg_replace('/[\s-]+/', '-', $slug);
    
    // Remove leading/trailing hyphens
    $slug = trim($slug, '-');
    
    return $slug;
}

/**
 * Generate SEO-friendly URL for movie
 */
function getMovieSeoUrl($movie_id, $title, $year) {
    $slug = generateSlug($title);
    return SITE_URL . "/movie/" . $slug . "-" . $year . "-" . $movie_id;
}

/**
 * Generate SEO-friendly URL for TV show
 */
function getTvShowSeoUrl($tvshow_id, $title, $year, $season = null) {
    $slug = generateSlug($title);
    $url = SITE_URL . "/tv-show/" . $slug . "-" . $year . "-" . $tvshow_id;
    
    if ($season) {
        $url .= "/season/" . $season;
    }
    
    return $url;
}

/**
 * Generate download URL for movie
 */
function getMovieDownloadUrl($movie_id, $title, $year) {
    $slug = generateSlug($title);
    return SITE_URL . "/download/movie/" . $slug . "-" . $year . "-" . $movie_id;
}

/**
 * Generate watch URL for movie
 */
function getMovieWatchUrl($movie_id, $title, $year) {
    $slug = generateSlug($title);
    return SITE_URL . "/watch/movie/" . $slug . "-" . $year . "-" . $movie_id;
}

/**
 * Generate watch URL for TV show
 */
function getTvShowWatchUrl($tvshow_id, $title, $year) {
    $slug = generateSlug($title);
    return SITE_URL . "/watch/tv-show/" . $slug . "-" . $year . "-" . $tvshow_id;
}

/**
 * Generate search URL
 */
function getSearchUrl($query) {
    $slug = generateSlug($query);
    return SITE_URL . "/search/" . urlencode($slug);
}

/**
 * Generate category URL for movies
 */
function getMovieCategoryUrl($category_slug) {
    return SITE_URL . "/movies/" . $category_slug;
}

/**
 * Generate category URL for TV shows
 */
function getTvShowCategoryUrl($category_slug) {
    return SITE_URL . "/tv-shows/" . $category_slug;
}

/**
 * Generate year-based URL for movies
 */
function getMovieYearUrl($year) {
    return SITE_URL . "/movies/" . $year;
}

/**
 * Generate year-based URL for TV shows
 */
function getTvShowYearUrl($year) {
    return SITE_URL . "/tv-shows/" . $year;
}

/**
 * Generate quality-based URL for movies
 */
function getMovieQualityUrl($quality) {
    return SITE_URL . "/movies/" . strtolower($quality);
}

/**
 * Generate language-based URL for movies
 */
function getMovieLanguageUrl($language) {
    return SITE_URL . "/movies/" . strtolower($language);
}

/**
 * Generate breadcrumb for movie details
 */
function getMovieBreadcrumb($movie) {
    $breadcrumb = [
        ['name' => 'Home', 'url' => SITE_URL],
        ['name' => 'Movies', 'url' => SITE_URL . '/movies.php'],
        ['name' => $movie['category_name'], 'url' => SITE_URL . '/movies.php?category=' . $movie['category_id']],
        ['name' => $movie['title'], 'url' => '']
    ];
    
    return $breadcrumb;
}

/**
 * Generate breadcrumb for TV show details
 */
function getTvShowBreadcrumb($tvshow) {
    $breadcrumb = [
        ['name' => 'Home', 'url' => SITE_URL],
        ['name' => 'TV Shows', 'url' => SITE_URL . '/tvshows.php'],
        ['name' => $tvshow['category_name'], 'url' => SITE_URL . '/tvshows.php?category=' . $tvshow['category_id']],
        ['name' => $tvshow['title'], 'url' => '']
    ];
    
    return $breadcrumb;
}

/**
 * Generate meta keywords for movie
 */
function getMovieKeywords($movie) {
    $keywords = [
        $movie['title'],
        $movie['title'] . ' download',
        $movie['title'] . ' full movie',
        $movie['title'] . ' ' . $movie['release_year'],
        $movie['title'] . ' HD',
        $movie['title'] . ' 720p',
        $movie['title'] . ' 1080p',
        $movie['category_name'] . ' movie',
        'Bengali movie',
        'movie download',
        'HD movie',
        'free movie download'
    ];
    
    if (!empty($movie['language'])) {
        $keywords[] = $movie['language'] . ' movie';
    }
    
    return implode(', ', $keywords);
}

/**
 * Generate meta keywords for TV show
 */
function getTvShowKeywords($tvshow) {
    $keywords = [
        $tvshow['title'],
        $tvshow['title'] . ' watch online',
        $tvshow['title'] . ' TV series',
        $tvshow['title'] . ' episodes',
        $tvshow['title'] . ' ' . $tvshow['release_year'],
        $tvshow['category_name'] . ' series',
        'Bengali series',
        'TV show online',
        'series streaming',
        'watch series free'
    ];
    
    if (!empty($tvshow['language'])) {
        $keywords[] = $tvshow['language'] . ' series';
    }
    
    return implode(', ', $keywords);
}

/**
 * Generate related search terms for content
 */
function getRelatedSearchTerms($title, $type = 'movie') {
    $terms = [];
    $base_terms = [
        'download', 'watch online', 'full ' . $type, 'HD', '720p', '1080p', 
        'free download', 'streaming', 'online'
    ];
    
    foreach ($base_terms as $term) {
        $terms[] = $title . ' ' . $term;
    }
    
    return $terms;
}



/**
 * Generate FAQ schema for movie/TV show
 */
function generateFAQSchema($title, $type = 'movie') {
    $faqs = [
        [
            'question' => "How to download " . $title . "?",
            'answer' => "You can download " . $title . " from our website in various qualities including 720p, 1080p and HD."
        ],
        [
            'question' => "Is " . $title . " available for free?",
            'answer' => "Yes, " . $title . " is available for free streaming and download on our platform."
        ],
        [
            'question' => "What quality is available for " . $title . "?",
            'answer' => $title . " is available in multiple qualities including 720p, 1080p, and HD quality."
        ]
    ];
    
    if ($type === 'tvshow') {
        $faqs[] = [
            'question' => "How many episodes are there in " . $title . "?",
            'answer' => $title . " has multiple episodes across different seasons. Check the episode list for complete details."
        ];
    }
    
    return $faqs;
}

/**
 * Check if current page is mobile
 */
function isMobile() {
    return preg_match("/(android|avantgo|blackberry|bolt|boost|cricket|docomo|fone|hiptop|mini|mobi|palm|phone|pie|tablet|up\.browser|up\.link|webos|wos)/i", $_SERVER["HTTP_USER_AGENT"]);
}

/**
 * Generate canonical URL
 */
function getCanonicalUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $uri = $_SERVER['REQUEST_URI'];
    
    // Remove query parameters for canonical URL
    $uri = strtok($uri, '?');
    
    return $protocol . '://' . $host . $uri;
}
?>
