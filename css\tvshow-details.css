/* TV Show Details Page Styles */

/* Banner Section */
.details-banner {
    position: relative;
    background-size: cover;
    background-position: center;
    padding: 100px 0 50px;
    color: #fff;
}

.banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.9) 100%);
}

.details-content {
    position: relative;
    z-index: 1;
}

.poster-container {
    position: relative;
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
    border-radius: 8px;
    overflow: hidden;
}

.details-poster {
    width: 100%;
    border-radius: 8px;
    display: block;
}

.premium-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #dc3545;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
}

.details-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.details-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    font-size: 0.9rem;
}

.details-rating {
    color: #ffc107;
    font-weight: bold;
}

.details-description h4 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: #f8f9fa;
}

.details-description p {
    color: #adb5bd;
    line-height: 1.6;
}

.details-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

/* Section Header */
.section-header {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #343a40;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #fff;
    margin-bottom: 0;
}

.section-title i {
    color: #dc3545;
}

/* Episode List */
.episode-list {
    display: grid;
    gap: 25px;
    margin-top: 30px;
}

.episode-item {
    background: linear-gradient(135deg, rgba(33, 37, 41, 0.95) 0%, rgba(52, 58, 64, 0.85) 100%);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.episode-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #dc3545, #e83e8c, #fd7e14, #ffc107);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.episode-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.4);
}

.episode-item:hover::before {
    opacity: 1;
}

.episode-header {
    display: flex;
    align-items: center;
    padding: 20px 25px;
    background: linear-gradient(135deg, rgba(33, 37, 41, 0.95) 0%, rgba(52, 58, 64, 0.9) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    position: relative;
}

.episode-number {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    color: white;
    padding: 12px 16px;
    border-radius: 12px;
    font-weight: 700;
    margin-right: 20px;
    min-width: 70px;
    text-align: center;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    position: relative;
    overflow: hidden;
}

.episode-number::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.episode-item:hover .episode-number::before {
    left: 100%;
}

.episode-title {
    flex-grow: 1;
}

.episode-title h5 {
    margin-bottom: 8px;
    font-size: 1.2rem;
    color: #fff;
    font-weight: 600;
    line-height: 1.3;
}

.episode-meta {
    display: flex;
    gap: 20px;
    font-size: 0.85rem;
    color: #adb5bd;
    align-items: center;
}

.episode-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.premium-tag {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: #000;
    font-weight: 700;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 10px rgba(255, 193, 7, 0.3);
    animation: premium-pulse 2s infinite;
}

@keyframes premium-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.episode-content {
    display: flex;
    padding: 25px;
    gap: 20px;
}

.episode-thumbnail {
    width: 200px;
    min-width: 200px;
    height: 120px;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

.episode-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.episode-item:hover .episode-thumbnail img {
    transform: scale(1.1);
}

.episode-thumbnail::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(220, 53, 69, 0.1), rgba(232, 62, 140, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.episode-item:hover .episode-thumbnail::after {
    opacity: 1;
}

.episode-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.episode-details {
    flex-grow: 1;
}

.episode-description {
    color: #adb5bd;
    font-size: 0.9rem;
    margin-bottom: 15px;
    line-height: 1.5;
}

.episode-actions {
    display: flex;
    gap: 10px;
}

.episode-downloads {
    background: linear-gradient(135deg, rgba(33, 37, 41, 0.8) 0%, rgba(52, 58, 64, 0.6) 100%);
    padding: 25px;
    border-top: 1px solid rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
}

.downloads-header {
    font-size: 1rem;
    font-weight: 700;
    color: #fff;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.downloads-header i {
    color: #dc3545;
    font-size: 1.1rem;
}

.download-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.download-link {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, rgba(13, 110, 253, 0.2) 0%, rgba(13, 110, 253, 0.1) 100%);
    border: 2px solid rgba(13, 110, 253, 0.3);
    border-radius: 16px;
    padding: 16px 20px;
    color: #fff;
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
}

.download-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.6s;
}

.download-link:hover::before {
    left: 100%;
}

.download-link:hover {
    background: linear-gradient(135deg, rgba(13, 110, 253, 0.3) 0%, rgba(13, 110, 253, 0.2) 100%);
    color: #fff;
    text-decoration: none;
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    border-color: rgba(13, 110, 253, 0.5);
}

.premium-link {
    background-color: rgba(220, 53, 69, 0.15);
    border: 2px solid rgba(220, 53, 69, 0.5);
    background-image: linear-gradient(to right, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.2));
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.premium-link::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, #dc3545, #e83e8c, #fd7e14, #ffc107, #e83e8c, #dc3545);
    background-size: 400% 400%;
    z-index: -1;
    filter: blur(10px);
    opacity: 0.5;
    animation: premium-background 15s ease infinite;
}

@keyframes premium-background {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.premium-link:hover {
    background-color: rgba(220, 53, 69, 0.25);
    background-image: linear-gradient(to right, rgba(220, 53, 69, 0.15), rgba(220, 53, 69, 0.3));
    transform: translateY(-3px);
    box-shadow: 0 7px 14px rgba(0, 0, 0, 0.3);
}

/* Premium Download Container */
.premium-download-container {
    position: relative;
    margin-bottom: 15px;
}

.premium-banner {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background-color: #dc3545;
    color: white;
    text-align: center;
    padding: 5px 0;
    font-weight: bold;
    border-radius: 5px 5px 0 0;
    z-index: 1;
    font-size: 0.9rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    letter-spacing: 1px;
}

.premium-banner i {
    color: #ffc107;
    margin-right: 5px;
    animation: crown-pulse 1.5s infinite;
}

@keyframes crown-pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

.premium-download-container .download-link {
    margin-top: 30px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-color: #dc3545;
}

.premium-download-container .download-button {
    background-color: rgba(220, 53, 69, 0.2);
    color: #dc3545;
}

.download-quality {
    background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 700;
    margin-right: 15px;
    box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.download-quality::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.download-link:hover .download-quality::before {
    left: 100%;
}

.premium-quality {
    background-color: #dc3545;
    background-image: linear-gradient(to right, #dc3545, #e83e8c);
    color: white;
    box-shadow: 0 2px 6px rgba(220, 53, 69, 0.4);
    border: 2px solid #ffc107;
    animation: premium-glow 2s infinite alternate;
}

@keyframes premium-glow {
    from {
        box-shadow: 0 0 5px #ffc107;
    }
    to {
        box-shadow: 0 0 15px #ffc107;
    }
}

.premium-quality i {
    margin-left: 5px;
    color: #ffc107;
    font-size: 0.7rem;
    vertical-align: 1px;
}

.download-server {
    color: #e9ecef;
    font-size: 0.9rem;
    margin-right: 15px;
    flex-grow: 1;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.download-server::before {
    content: '🌐';
    font-size: 0.8rem;
}

.premium-server {
    color: #ffc107;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.premium-server::before {
    content: '👑';
}

.download-size {
    color: #adb5bd;
    font-size: 0.85rem;
    margin-right: 15px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
    padding: 6px 14px;
    border-radius: 12px;
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
}

.premium-size {
    background-color: rgba(220, 53, 69, 0.15);
    color: #ffc107;
    border: 1px solid rgba(220, 53, 69, 0.3);
    position: relative;
    font-weight: bold;
}

.premium-size::before {
    content: 'PREMIUM';
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 8px;
    background-color: #dc3545;
    color: white;
    padding: 2px 5px;
    border-radius: 3px;
    font-weight: bold;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.download-button {
    width: 44px;
    height: 44px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.download-button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
    border-radius: 50%;
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
}

.download-link:hover .download-button {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.2) 100%);
    transform: scale(1.15) rotate(10deg);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.3);
}

.download-link:hover .download-button::before {
    width: 100%;
    height: 100%;
}

.download-button i {
    font-size: 1.1rem;
    z-index: 1;
}

/* Download link container for download and play buttons */
.download-link-container {
    display: flex;
    width: 100%;
    margin-bottom: 10px;
}

.download-link-container .download-link {
    flex: 1;
    width: auto;
    border-radius: 5px 0 0 5px;
    margin: 0;
}

/* Play link styles */
.play-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #e50914;
    padding: 15px 10px;
    border-radius: 0 5px 5px 0;
    transition: all 0.3s ease;
    text-decoration: none;
    color: #fff;
    width: 60px;
}

.play-link:hover {
    background-color: #f40612;
    color: #fff;
}

.play-link-icon {
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.play-link-text {
    font-size: 0.8rem;
    text-align: center;
}

/* Stream Container */
.stream-container, .download-container {
    padding: 20px;
    background-color: rgba(33, 37, 41, 0.5);
    border-radius: 8px;
}

.stream-header, .download-header {
    margin-bottom: 20px;
    border-bottom: 1px solid #343a40;
    padding-bottom: 15px;
}

/* Download Button */
.download-button {
    display: flex;
    align-items: center;
    background-color: #007bff;
    color: white;
    padding: 12px 15px;
    border-radius: 5px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.download-button:hover {
    background-color: #0069d9;
    color: white;
    text-decoration: none;
}

.download-icon {
    background-color: rgba(255,255,255,0.2);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.2rem;
}

.download-info {
    display: flex;
    flex-direction: column;
}

.download-text {
    font-weight: bold;
    font-size: 0.9rem;
}

.download-meta {
    font-size: 0.75rem;
    opacity: 0.8;
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
    .download-links {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

@media (max-width: 992px) {
    .episode-list {
        gap: 20px;
    }

    .episode-header {
        padding: 18px 20px;
    }

    .episode-content {
        padding: 20px;
    }

    .episode-thumbnail {
        width: 160px;
        min-width: 160px;
        height: 100px;
    }
}

@media (max-width: 768px) {
    .details-title {
        font-size: 1.8rem;
    }

    .details-banner {
        padding: 80px 0 40px;
    }

    .details-meta {
        gap: 10px;
        font-size: 0.8rem;
    }

    .custom-tab-buttons {
        justify-content: center;
    }

    .episode-list {
        gap: 15px;
        margin-top: 20px;
    }

    .episode-header {
        padding: 15px 18px;
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .episode-number {
        margin-right: 0;
        align-self: flex-start;
    }

    .episode-title h5 {
        font-size: 1.1rem;
    }

    .episode-meta {
        flex-wrap: wrap;
        gap: 10px;
    }

    .episode-content {
        flex-direction: column;
        padding: 18px;
        gap: 15px;
    }

    .episode-thumbnail {
        width: 100%;
        height: 180px;
        margin-right: 0;
    }

    .episode-downloads {
        padding: 18px;
    }

    .download-links {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .download-link {
        padding: 14px 16px;
    }

    .download-quality {
        padding: 6px 12px;
        font-size: 0.8rem;
    }

    .download-server {
        font-size: 0.85rem;
    }

    .download-size {
        font-size: 0.8rem;
        padding: 5px 10px;
    }

    .download-button {
        width: 38px;
        height: 38px;
    }
}

@media (max-width: 576px) {
    .details-title {
        font-size: 1.5rem;
    }

    .details-banner {
        padding: 60px 0 30px;
    }

    .tab-button {
        padding: 8px 15px;
        font-size: 0.9rem;
    }

    .episode-list {
        gap: 12px;
    }

    .episode-item {
        border-radius: 12px;
    }

    .episode-header {
        padding: 12px 15px;
        gap: 12px;
    }

    .episode-number {
        padding: 8px 12px;
        font-size: 0.8rem;
        min-width: 60px;
    }

    .episode-title h5 {
        font-size: 1rem;
    }

    .episode-meta {
        flex-direction: column;
        gap: 8px;
        font-size: 0.8rem;
    }

    .episode-content {
        padding: 15px;
        gap: 12px;
    }

    .episode-thumbnail {
        height: 160px;
    }

    .episode-downloads {
        padding: 15px;
    }

    .downloads-header {
        font-size: 0.9rem;
        margin-bottom: 15px;
    }

    .download-link {
        padding: 12px 14px;
        border-radius: 12px;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .download-quality {
        margin-right: 0;
        margin-bottom: 5px;
    }

    .download-server {
        margin-right: 0;
        font-size: 0.8rem;
    }

    .download-size {
        margin-right: 0;
        font-size: 0.75rem;
    }

    .download-button {
        width: 35px;
        height: 35px;
        position: absolute;
        top: 12px;
        right: 12px;
    }

    .episode-actions {
        flex-direction: column;
        gap: 10px;
    }

    .premium-tag {
        font-size: 0.7rem;
        padding: 3px 8px;
    }
}

/* Extra small devices */
@media (max-width: 480px) {
    .episode-header {
        padding: 10px 12px;
    }

    .episode-content {
        padding: 12px;
    }

    .episode-downloads {
        padding: 12px;
    }

    .download-link {
        padding: 10px 12px;
    }

    .episode-thumbnail {
        height: 140px;
    }
}

/* Loading Animation */
.episode-item.loading {
    opacity: 0.7;
    pointer-events: none;
}

.episode-item.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Scroll animations */
.episode-item {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.6s ease forwards;
}

.episode-item:nth-child(1) { animation-delay: 0.1s; }
.episode-item:nth-child(2) { animation-delay: 0.2s; }
.episode-item:nth-child(3) { animation-delay: 0.3s; }
.episode-item:nth-child(4) { animation-delay: 0.4s; }
.episode-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced focus states for accessibility */
.download-link:focus {
    outline: 3px solid rgba(13, 110, 253, 0.5);
    outline-offset: 2px;
}

.episode-item:focus-within {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.3);
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
    .episode-item {
        background: linear-gradient(135deg, rgba(20, 24, 28, 0.95) 0%, rgba(33, 37, 41, 0.85) 100%);
        border-color: rgba(255, 255, 255, 0.05);
    }

    .episode-header {
        background: linear-gradient(135deg, rgba(20, 24, 28, 0.95) 0%, rgba(33, 37, 41, 0.9) 100%);
    }

    .episode-downloads {
        background: linear-gradient(135deg, rgba(20, 24, 28, 0.8) 0%, rgba(33, 37, 41, 0.6) 100%);
    }
}

/* Print styles */
@media print {
    .episode-item {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }

    .download-links {
        display: none;
    }
}
