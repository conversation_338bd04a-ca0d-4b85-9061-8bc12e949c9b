/* Download Pop-under Modal Styles */
.download-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.download-modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.download-modal {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.8) translateY(50px);
    transition: all 0.3s ease;
    border: 1px solid #444;
}

.download-modal-overlay.show .download-modal {
    transform: scale(1) translateY(0);
}

.download-modal-header {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #444;
}

.download-modal-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.download-modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.download-modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

.download-modal-body {
    padding: 30px;
    color: white;
    text-align: center;
}

.download-progress {
    background: #333;
    border-radius: 25px;
    height: 8px;
    margin: 20px 0;
    overflow: hidden;
    position: relative;
}

.download-progress-bar {
    background: linear-gradient(90deg, #28a745, #20c997);
    height: 100%;
    width: 0%;
    border-radius: 25px;
    transition: width 1s ease;
    position: relative;
}

.download-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.download-info {
    margin: 25px 0;
}

.download-message {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 15px;
    color: #fff;
}

.download-message span {
    color: #28a745;
    font-weight: bold;
    font-size: 20px;
}

.download-ads-notice {
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid #ffc107;
    border-radius: 8px;
    padding: 12px;
    color: #ffc107;
    font-size: 14px;
    margin: 15px 0;
}

.download-premium-offer {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #000;
    padding: 20px;
    border-radius: 10px;
    margin-top: 20px;
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

.download-premium-offer p {
    margin: 0 0 15px 0;
    font-weight: 600;
}

.download-premium-offer .btn {
    background: #dc3545;
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.download-premium-offer .btn:hover {
    background: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
}

/* Success Toast */
.download-success-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 15px 25px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);
    z-index: 10001;
    transform: translateX(400px);
    transition: all 0.3s ease;
    font-weight: 500;
}

.download-success-toast.show {
    transform: translateX(0);
}

/* Responsive Design */
@media (max-width: 768px) {
    .download-modal {
        width: 95%;
        margin: 10px;
    }
    
    .download-modal-body {
        padding: 20px;
    }
    
    .download-modal-header {
        padding: 15px;
    }
    
    .download-modal-header h4 {
        font-size: 16px;
    }
    
    .download-message {
        font-size: 16px;
    }
    
    .download-premium-offer {
        padding: 15px;
    }
}

/* Loading Animation */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.download-modal-body .fas.fa-download {
    animation: pulse 2s infinite;
}

/* Countdown Animation */
@keyframes countdown {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.download-message span {
    animation: countdown 1s infinite;
}

/* Premium Badge Animation */
@keyframes glow {
    0% { box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3); }
    50% { box-shadow: 0 5px 25px rgba(255, 215, 0, 0.6); }
    100% { box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3); }
}

.download-premium-offer {
    animation: glow 2s infinite;
}

/* Dark Theme Enhancements */
.download-modal-overlay {
    background: rgba(0, 0, 0, 0.9);
}

.download-modal {
    border: 1px solid #444;
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.7),
        0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Smooth Transitions */
* {
    transition: all 0.3s ease;
}

/* Focus States */
.download-modal-close:focus {
    outline: 2px solid #ffc107;
    outline-offset: 2px;
}

.download-premium-offer .btn:focus {
    outline: 2px solid #000;
    outline-offset: 2px;
}

/* Additional Download Button Styles */
.download-btn {
    cursor: pointer;
    position: relative;
}

.download-btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(220, 53, 69, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: inherit;
}

.download-btn:hover::after {
    opacity: 1;
}
