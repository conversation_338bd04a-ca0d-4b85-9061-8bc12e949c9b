/**
 * Safe Pop-under Implementation
 * Ensures main tab doesn't close and user experience remains good
 */

document.addEventListener('DOMContentLoaded', function() {
    // Only show pop-under for non-premium users
    if (typeof isPremiumUser !== 'undefined' && isPremiumUser) {
        return; // Don't show pop-under for premium users
    }
    
    let popunderShown = false;
    let userInteracted = false;
    
    // Track user interaction
    document.addEventListener('click', function() {
        userInteracted = true;
    });
    
    document.addEventListener('scroll', function() {
        userInteracted = true;
    });
    
    // Safe pop-under function
    function showSafePopunder() {
        if (popunderShown || !userInteracted) {
            return;
        }
        
        try {
            // Create a new window/tab that opens behind current tab
            const popunder = window.open('', '_blank');
            
            if (popunder) {
                // Ensure main window stays focused
                window.focus();
                
                // Write content to pop-under
                popunder.document.write(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>CinePix - Premium Movies & TV Shows</title>
                        <meta charset="utf-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1">
                        <style>
                            body {
                                font-family: Arial, sans-serif;
                                background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
                                color: white;
                                margin: 0;
                                padding: 20px;
                                text-align: center;
                            }
                            .container {
                                max-width: 800px;
                                margin: 0 auto;
                                padding: 40px 20px;
                            }
                            .logo {
                                font-size: 2.5rem;
                                color: #e50914;
                                margin-bottom: 20px;
                                font-weight: bold;
                            }
                            .message {
                                font-size: 1.2rem;
                                margin-bottom: 30px;
                                line-height: 1.6;
                            }
                            .btn {
                                display: inline-block;
                                background: #e50914;
                                color: white;
                                padding: 15px 30px;
                                text-decoration: none;
                                border-radius: 5px;
                                font-size: 1.1rem;
                                margin: 10px;
                                transition: background 0.3s;
                            }
                            .btn:hover {
                                background: #b8070f;
                            }
                            .features {
                                margin-top: 40px;
                                text-align: left;
                            }
                            .feature {
                                margin: 15px 0;
                                padding: 10px;
                                background: rgba(255,255,255,0.1);
                                border-radius: 5px;
                            }
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <div class="logo">🎬 CinePix</div>
                            <div class="message">
                                <h2>প্রিমিয়াম সদস্যতা নিন এবং বিজ্ঞাপন ছাড়াই উপভোগ করুন!</h2>
                                <p>আমাদের প্রিমিয়াম প্ল্যানে যোগ দিন এবং পান:</p>
                            </div>
                            
                            <div class="features">
                                <div class="feature">✅ বিজ্ঞাপন মুক্ত অভিজ্ঞতা</div>
                                <div class="feature">✅ সর্বোচ্চ গুণমানের ভিডিও</div>
                                <div class="feature">✅ দ্রুত ডাউনলোড লিংক</div>
                                <div class="feature">✅ এক্সক্লুসিভ কন্টেন্ট</div>
                                <div class="feature">✅ ২৪/৭ সাপোর্ট</div>
                            </div>
                            
                            <div style="margin-top: 30px;">
                                <a href="https://cinepix.top/premium.php" class="btn">প্রিমিয়াম প্ল্যান দেখুন</a>
                                <a href="https://cinepix.top" class="btn" style="background: #333;">সাইটে ফিরে যান</a>
                            </div>
                            
                            <div style="margin-top: 40px; font-size: 0.9rem; opacity: 0.7;">
                                <p>এই উইন্ডোটি ৫ সেকেন্ড পর স্বয়ংক্রিয়ভাবে বন্ধ হয়ে যাবে</p>
                            </div>
                        </div>
                        
                        <script>
                            // Auto close after 5 seconds
                            setTimeout(function() {
                                window.close();
                            }, 5000);
                            
                            // Close on click outside
                            document.addEventListener('click', function(e) {
                                if (e.target.tagName !== 'A') {
                                    setTimeout(function() {
                                        window.close();
                                    }, 1000);
                                }
                            });
                        </script>
                    </body>
                    </html>
                `);
                
                popunder.document.close();
                
                // Ensure main window stays active
                setTimeout(function() {
                    window.focus();
                }, 100);
                
                popunderShown = true;
                
                // Store in session to prevent multiple pop-unders
                sessionStorage.setItem('popunderShown', 'true');
            }
        } catch (error) {
            console.log('Pop-under blocked or failed:', error);
        }
    }
    
    // Show pop-under after user interaction and some delay
    setTimeout(function() {
        if (userInteracted && !sessionStorage.getItem('popunderShown')) {
            showSafePopunder();
        }
    }, 3000); // 3 seconds after page load
    
    // Also try on scroll (if not shown yet)
    let scrollTimeout;
    window.addEventListener('scroll', function() {
        if (!popunderShown && !sessionStorage.getItem('popunderShown')) {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(function() {
                showSafePopunder();
            }, 2000);
        }
    });
    
    // Try on click (if not shown yet)
    document.addEventListener('click', function() {
        if (!popunderShown && !sessionStorage.getItem('popunderShown')) {
            setTimeout(function() {
                showSafePopunder();
            }, 1000);
        }
    });
});

// Function to check if user is premium (will be set by PHP)
window.isPremiumUser = false;
