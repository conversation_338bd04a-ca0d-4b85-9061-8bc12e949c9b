<?php
require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Check if episode ID is provided
if (!isset($_GET['episode']) || empty($_GET['episode'])) {
    redirect('tvshows.php');
}

$episode_id = (int)$_GET['episode'];

// Get episode details
$episode_query = "SELECT e.*, t.title as tvshow_title, t.poster as tvshow_poster
                 FROM episodes e
                 JOIN tvshows t ON e.tvshow_id = t.id
                 WHERE e.id = $episode_id";
$episode_result = mysqli_query($conn, $episode_query);

if (mysqli_num_rows($episode_result) == 0) {
    redirect('tvshows.php');
}

$episode = mysqli_fetch_assoc($episode_result);

// Delete link
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $link_id = (int)$_GET['delete'];

    $delete_query = "DELETE FROM episode_links WHERE id = $link_id AND episode_id = $episode_id";

    if (mysqli_query($conn, $delete_query)) {
        $success_message = 'Link deleted successfully.';
    } else {
        $error_message = 'Error deleting link: ' . mysqli_error($conn);
    }
}

// Add/Edit Download Link
if (isset($_POST['add_download_link'])) {
    $quality = sanitize($_POST['quality']);
    $link_url = sanitize($_POST['link_url']);
    $server_name = isset($_POST['server_name']) ? sanitize($_POST['server_name']) : NULL;
    $file_size = isset($_POST['file_size']) ? sanitize($_POST['file_size']) : NULL;
    $subtitle_url_bn = isset($_POST['subtitle_url_bn']) ? sanitize($_POST['subtitle_url_bn']) : NULL;
    $subtitle_url_en = isset($_POST['subtitle_url_en']) ? sanitize($_POST['subtitle_url_en']) : NULL;
    $is_premium = isset($_POST['is_premium']) ? 1 : 0;
    $link_id = isset($_POST['link_id']) ? (int)$_POST['link_id'] : 0;

    if (empty($quality) || empty($link_url)) {
        $error_message = 'Please fill in all required fields.';
    } else {
        if ($link_id > 0) {
            // Update existing link
            $update_query = "UPDATE episode_links SET
                            quality = '$quality',
                            server_name = " . ($server_name ? "'$server_name'" : "NULL") . ",
                            file_size = " . ($file_size ? "'$file_size'" : "NULL") . ",
                            link_url = '$link_url',
                            subtitle_url_bn = " . ($subtitle_url_bn ? "'$subtitle_url_bn'" : "NULL") . ",
                            subtitle_url_en = " . ($subtitle_url_en ? "'$subtitle_url_en'" : "NULL") . ",
                            is_premium = $is_premium
                            WHERE id = $link_id AND episode_id = $episode_id AND link_type = 'download'";

            if (mysqli_query($conn, $update_query)) {
                $success_message = 'Download link updated successfully.';
            } else {
                $error_message = 'Error updating download link: ' . mysqli_error($conn);
            }
        } else {
            // Check if file_size column exists
            $check_column_query = "SHOW COLUMNS FROM episode_links LIKE 'file_size'";
            $column_result = mysqli_query($conn, $check_column_query);

            if (mysqli_num_rows($column_result) > 0) {
                // Column exists, include it in the query
                $insert_query = "INSERT INTO episode_links (episode_id, link_type, quality, server_name, file_size, link_url, subtitle_url_bn, subtitle_url_en, is_premium)
                                VALUES ($episode_id, 'download', '$quality', " . ($server_name ? "'$server_name'" : "NULL") . ", " . ($file_size ? "'$file_size'" : "NULL") . ", '$link_url', " . ($subtitle_url_bn ? "'$subtitle_url_bn'" : "NULL") . ", " . ($subtitle_url_en ? "'$subtitle_url_en'" : "NULL") . ", $is_premium)";
            } else {
                // Column doesn't exist, exclude it from the query
                $insert_query = "INSERT INTO episode_links (episode_id, link_type, quality, server_name, link_url, subtitle_url_bn, subtitle_url_en, is_premium)
                                VALUES ($episode_id, 'download', '$quality', " . ($server_name ? "'$server_name'" : "NULL") . ", '$link_url', " . ($subtitle_url_bn ? "'$subtitle_url_bn'" : "NULL") . ", " . ($subtitle_url_en ? "'$subtitle_url_en'" : "NULL") . ", $is_premium)";
            }

            if (mysqli_query($conn, $insert_query)) {
                $success_message = 'Download link added successfully.';

                // Add file_size column if it doesn't exist
                if (mysqli_num_rows($column_result) == 0) {
                    $add_column_query = "ALTER TABLE episode_links ADD COLUMN file_size VARCHAR(50) NULL AFTER server_name";
                    mysqli_query($conn, $add_column_query);
                }
            } else {
                $error_message = 'Error adding download link: ' . mysqli_error($conn);
            }
        }
    }
}



// Get existing links
$download_query = "SELECT * FROM episode_links WHERE episode_id = $episode_id AND link_type = 'download' ORDER BY quality DESC";
$download_result = mysqli_query($conn, $download_query);

// Edit download link
$edit_download_link = null;
if (isset($_GET['edit_download']) && !empty($_GET['edit_download'])) {
    $link_id = (int)$_GET['edit_download'];

    $edit_query = "SELECT * FROM episode_links WHERE id = $link_id AND episode_id = $episode_id AND link_type = 'download'";
    $edit_result = mysqli_query($conn, $edit_query);

    if (mysqli_num_rows($edit_result) > 0) {
        $edit_download_link = mysqli_fetch_assoc($edit_result);
    }
}


?>

<?php
// Set page title
$page_title = "Manage Episode Links";

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<style>
    .action-buttons .btn {
        min-width: 100px;
        margin-bottom: 5px;
    }

    .action-buttons-left {
        display: flex;
        justify-content: flex-start;
        gap: 5px;
    }

    @media (max-width: 768px) {
        .action-buttons, .action-buttons-left {
            display: flex;
            flex-direction: column;
        }

        .action-buttons .btn, .action-buttons-left .btn {
            margin-bottom: 5px;
        }
    }

    /* New styles for better design */
    .episode-info {
        background-color: #2c3e50;
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .episode-details {
        padding: 10px 0;
    }

    .episode-details h4 {
        color: #3498db;
        margin-bottom: 10px;
        font-weight: 700;
    }

    .episode-details h5 {
        font-size: 1.2rem;
        margin-bottom: 15px;
        color: #ecf0f1;
    }

    .episode-details p {
        font-size: 1rem;
        color: #bdc3c7;
    }

    .card {
        border: none;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 25px;
        border-radius: 10px;
        overflow: hidden;
    }

    .card-header {
        padding: 15px 20px;
        font-weight: 600;
    }

    .card-body {
        padding: 25px;
    }

    .table {
        border-collapse: separate;
        border-spacing: 0;
    }

    .table th {
        background-color: #f8f9fa;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
    }

    .table td, .table th {
        padding: 12px 15px;
        vertical-align: middle;
    }

    .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.02);
    }

    .btn {
        border-radius: 5px;
        font-weight: 500;
        padding: 8px 15px;
        transition: all 0.3s;
    }

    .btn-primary {
        background-color: #3498db;
        border-color: #3498db;
    }

    .btn-primary:hover {
        background-color: #2980b9;
        border-color: #2980b9;
    }

    .btn-success {
        background-color: #2ecc71;
        border-color: #2ecc71;
    }

    .btn-success:hover {
        background-color: #27ae60;
        border-color: #27ae60;
    }

    .btn-danger {
        background-color: #e74c3c;
        border-color: #e74c3c;
    }

    .btn-danger:hover {
        background-color: #c0392b;
        border-color: #c0392b;
    }

    .form-control, .form-select {
        padding: 10px 15px;
        border-radius: 5px;
        border: 1px solid #ddd;
    }

    .form-control:focus, .form-select:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.25rem rgba(52, 152, 219, 0.25);
    }

    .badge {
        padding: 6px 10px;
        font-weight: 500;
        border-radius: 4px;
    }

    /* Hide the poster image */
    .episode-poster {
        display: none;
    }
</style>

<!-- Main Content -->
<div class="content">
    <div class="container-fluid p-4">
        <!-- Page Heading -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <h1 class="h3 mb-0 text-gray-800">এপিসোড লিংক ম্যানেজমেন্ট</h1>
        </div>

        <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="episode-info">
            <div class="episode-details">
                <h4><?php echo $episode['tvshow_title']; ?></h4>
                <h5>সিজন <?php echo $episode['season_number']; ?>, এপিসোড <?php echo $episode['episode_number']; ?>: <?php echo $episode['title']; ?></h5>
                <div class="d-flex align-items-center mb-2">
                    <p class="mb-0 me-3"><i class="fas fa-clock me-2"></i><?php echo $episode['duration']; ?> মিনিট</p>
                    <?php if($episode['is_premium']): ?>
                    <span class="badge bg-danger"><i class="fas fa-crown me-1"></i> প্রিমিয়াম</span>
                    <?php endif; ?>
                </div>
                <div class="mt-3">
                    <a href="manage_episodes.php?tvshow=<?php echo $episode['tvshow_id']; ?>" class="btn btn-sm btn-outline-light">
                        <i class="fas fa-arrow-left me-1"></i> এপিসোড লিস্টে ফিরে যান
                    </a>
                    <a href="edit_episode.php?id=<?php echo $episode_id; ?>" class="btn btn-sm btn-outline-info ms-2">
                        <i class="fas fa-edit me-1"></i> এপিসোড এডিট করুন
                    </a>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Download Links -->
            <div class="col-md-12">
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">ডাউনলোড লিংক</h6>
                        <button type="button" class="btn btn-sm btn-primary" id="toggleAddLinkForm">
                            <i class="fas fa-plus me-1"></i> নতুন লিংক যোগ করুন
                        </button>
                    </div>
                    <div class="card-body">
                        <!-- Add Link Form (Initially Hidden) -->
                        <div id="addLinkForm" style="display: none;" class="mb-4 p-3 border rounded bg-light">
                            <h5 class="mb-3"><?php echo $edit_download_link ? 'ডাউনলোড লিংক আপডেট করুন' : 'নতুন ডাউনলোড লিংক যোগ করুন'; ?></h5>
                            <form method="post" action="">
                                <?php if ($edit_download_link): ?>
                                <input type="hidden" name="link_id" value="<?php echo $edit_download_link['id']; ?>">
                                <?php endif; ?>

                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="quality" class="form-label">কোয়ালিটি <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="quality" name="quality" value="<?php echo $edit_download_link ? $edit_download_link['quality'] : ''; ?>" required>
                                        <small class="form-text text-muted">উদাহরণ: 1080p, 720p, 480p, ইত্যাদি</small>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="server_name" class="form-label">সার্ভার নাম</label>
                                        <input type="text" class="form-control" id="server_name" name="server_name" value="<?php echo $edit_download_link ? $edit_download_link['server_name'] : ''; ?>">
                                        <small class="form-text text-muted">উদাহরণ: Google Drive, Mega, ইত্যাদি</small>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="file_size" class="form-label">ফাইল সাইজ</label>
                                        <input type="text" class="form-control" id="file_size" name="file_size" value="<?php echo $edit_download_link ? $edit_download_link['file_size'] : ''; ?>">
                                        <small class="form-text text-muted">উদাহরণ: 1.2GB, 800MB, ইত্যাদি</small>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="link_url" class="form-label">ডাউনলোড লিংক <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="link_url" name="link_url" value="<?php echo $edit_download_link ? $edit_download_link['link_url'] : ''; ?>" required>
                                </div>

                                <div class="mb-3">
                                    <button type="button" class="btn btn-sm btn-outline-primary" id="toggleSubtitleFields">
                                        <i class="fas fa-plus"></i> সাবটাইটেল যুক্ত করুন
                                    </button>
                                </div>

                                <div class="row mb-3 subtitle-fields" style="display: none;">
                                    <div class="col-md-6">
                                        <label for="subtitle_url_bn" class="form-label">বাংলা সাবটাইটেল লিংক</label>
                                        <input type="text" class="form-control" id="subtitle_url_bn" name="subtitle_url_bn" value="<?php echo $edit_download_link ? $edit_download_link['subtitle_url_bn'] : ''; ?>">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="subtitle_url_en" class="form-label">ইংরেজি সাবটাইটেল লিংক</label>
                                        <input type="text" class="form-control" id="subtitle_url_en" name="subtitle_url_en" value="<?php echo $edit_download_link ? $edit_download_link['subtitle_url_en'] : ''; ?>">
                                    </div>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="is_premium" name="is_premium" <?php echo $edit_download_link && $edit_download_link['is_premium'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_premium">
                                        প্রিমিয়াম লিংক
                                    </label>
                                </div>

                                <div class="d-flex gap-2">
                                    <button type="submit" name="add_download_link" class="btn btn-primary">
                                        <?php echo $edit_download_link ? 'আপডেট করুন' : 'যোগ করুন'; ?>
                                    </button>
                                    <button type="button" class="btn btn-secondary" id="cancelAddLink">বাতিল করুন</button>
                                </div>
                            </form>
                        </div>

                        <?php if (mysqli_num_rows($download_result) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>কোয়ালিটি</th>
                                        <th>সার্ভার</th>
                                        <th>সাইজ</th>
                                        <th>প্রিমিয়াম</th>
                                        <th>অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($link = mysqli_fetch_assoc($download_result)): ?>
                                    <tr>
                                        <td><?php echo $link['quality']; ?></td>
                                        <td><?php echo $link['server_name'] ? $link['server_name'] : 'N/A'; ?></td>
                                        <td><?php echo $link['file_size'] ? $link['file_size'] : 'N/A'; ?></td>
                                        <td>
                                            <?php if ($link['is_premium']): ?>
                                            <span class="badge bg-danger">হ্যাঁ</span>
                                            <?php else: ?>
                                            <span class="badge bg-success">না</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="action-buttons-left">
                                            <a href="?episode=<?php echo $episode_id; ?>&edit_download=<?php echo $link['id']; ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="?episode=<?php echo $episode_id; ?>&delete=<?php echo $link['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this link?');">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="alert alert-info">
                            কোন ডাউনলোড লিংক নেই। উপরের বাটন ক্লিক করে নতুন লিংক যোগ করুন।
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>


        </div>
    </div>
</div>





<?php
// JavaScript for form toggle and subtitle toggle
echo '<script>
    document.addEventListener("DOMContentLoaded", function() {
        // Toggle Add Link Form
        const toggleAddLinkBtn = document.getElementById("toggleAddLinkForm");
        const addLinkForm = document.getElementById("addLinkForm");
        const cancelAddLinkBtn = document.getElementById("cancelAddLink");

        if (toggleAddLinkBtn && addLinkForm) {
            toggleAddLinkBtn.addEventListener("click", function() {
                addLinkForm.style.display = "block";
            });

            if (cancelAddLinkBtn) {
                cancelAddLinkBtn.addEventListener("click", function() {
                    addLinkForm.style.display = "none";
                });
            }
        }

        // Toggle Subtitle Fields
        const toggleBtn = document.getElementById("toggleSubtitleFields");
        const subtitleFields = document.querySelector(".subtitle-fields");

        if (toggleBtn && subtitleFields) {
            toggleBtn.addEventListener("click", function() {
                if (subtitleFields.style.display === "none") {
                    subtitleFields.style.display = "flex";
                    toggleBtn.innerHTML = \'<i class="fas fa-minus"></i> সাবটাইটেল লুকান\';
                } else {
                    subtitleFields.style.display = "none";
                    toggleBtn.innerHTML = \'<i class="fas fa-plus"></i> সাবটাইটেল যুক্ত করুন\';
                }
            });

            // Show subtitle fields if they already have values
            const subtitleBn = document.getElementById("subtitle_url_bn");
            const subtitleEn = document.getElementById("subtitle_url_en");

            if ((subtitleBn && subtitleBn.value) || (subtitleEn && subtitleEn.value)) {
                subtitleFields.style.display = "flex";
                toggleBtn.innerHTML = \'<i class="fas fa-minus"></i> সাবটাইটেল লুকান\';
            }
        }

        // Show form if editing
        ' . ($edit_download_link ? 'addLinkForm.style.display = "block";' : '') . '
    });
</script>';

// Include footer
require_once 'includes/footer.php';
?>
