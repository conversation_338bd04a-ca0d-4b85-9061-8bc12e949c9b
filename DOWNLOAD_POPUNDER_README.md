# Download Pop-under Advertisement System

## বৈশিষ্ট্য (Features)

✅ **স্মার্ট ডাউনলোড সিস্টেম**: ডাউনলোড বাটনে ক্লিক করলে ৪-৫ সেকেন্ড পপআপ দেখানো হয়  
✅ **প্রিমিয়াম ব্যবহারকারী সুরক্ষা**: প্রিমিয়াম ব্যবহারকারীরা কোনো বিজ্ঞাপন দেখবেন না  
✅ **রেসপন্সিভ ডিজাইন**: মোবাইল এবং ডেস্কটপ উভয়ে কাজ করে  
✅ **সুন্দর UI/UX**: আকর্ষণীয় মোডাল এবং কাউন্টডাউন টাইমার  
✅ **বিজ্ঞাপন ইন্টিগ্রেশন**: একাধিক বিজ্ঞাপন নেটওয়ার্ক সাপোর্ট  
✅ **স্বয়ংক্রিয় ডাউনলোড**: টাইমার শেষ হলে স্বয়ংক্রিয়ভাবে ডাউনলোড শুরু  

## ইনস্টলেশন (Installation)

### ১. ফাইল আপলোড করুন:
```
js/download-popunder.js
css/download-popunder.css
```

### ২. Footer.php এ যোগ করুন:
```php
<!-- Download Pop-under System -->
<link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/download-popunder.css?v=<?php echo time(); ?>">
<script>
    window.isPremiumUser = <?php echo (isLoggedIn() && isPremiumUser()) ? 'true' : 'false'; ?>;
    window.SITE_URL = '<?php echo SITE_URL; ?>';
</script>
<script src="<?php echo SITE_URL; ?>/js/download-popunder.js?v=<?php echo time(); ?>"></script>
```

### ৩. ডাউনলোড বাটন আপডেট করুন:
```html
<!-- পুরাতন -->
<a href="download-link.mp4" class="download-link">Download</a>

<!-- নতুন -->
<a href="download-link.mp4" class="download-link download-btn" data-download-url="download-link.mp4">Download</a>
```

## কিভাবে কাজ করে (How It Works)

### বিনামূল্যে ব্যবহারকারীদের জন্য:
1. ডাউনলোড বাটনে ক্লিক করলে একটি মোডাল খুলে যায়
2. ৫ সেকেন্ড কাউন্টডাউন টাইমার শুরু হয়
3. একই সাথে একটি পপ-আন্ডার উইন্ডো খুলে যায় বিজ্ঞাপন সহ
4. কাউন্টডাউন শেষ হলে স্বয়ংক্রিয়ভাবে ডাউনলোড শুরু হয়
5. পপ-আন্ডার উইন্ডো ৬ সেকেন্ড পর বন্ধ হয়ে যায়

### প্রিমিয়াম ব্যবহারকারীদের জন্য:
1. ডাউনলোড বাটনে ক্লিক করলে সরাসরি ডাউনলোড শুরু হয়
2. কোনো বিজ্ঞাপন বা পপআপ দেখানো হয় না

## টেস্ট করুন (Testing)

1. `test_download_popup.html` ফাইল ব্রাউজারে খুলুন
2. "প্রিমিয়াম টগল করুন" বাটন দিয়ে প্রিমিয়াম স্ট্যাটাস পরিবর্তন করুন
3. বিভিন্ন ডাউনলোড বাটনে ক্লিক করে পরীক্ষা করুন

## কাস্টমাইজেশন (Customization)

### কাউন্টডাউন সময় পরিবর্তন:
```javascript
// js/download-popunder.js এ
let timeLeft = 5; // এখানে সেকেন্ড পরিবর্তন করুন
```

### বিজ্ঞাপন কোড পরিবর্তন:
```javascript
// js/download-popunder.js এর showPopunder() ফাংশনে
const popunderUrl = 'আপনার-বিজ্ঞাপন-URL';
```

### স্টাইল কাস্টমাইজেশন:
`css/download-popunder.css` ফাইল এডিট করুন

## সাপোর্টেড ব্রাউজার (Supported Browsers)

✅ Chrome 60+  
✅ Firefox 55+  
✅ Safari 12+  
✅ Edge 79+  
✅ Mobile browsers  

## ট্রাবলশুটিং (Troubleshooting)

### সমস্যা: পপআপ কাজ করছে না
**সমাধান**: ব্রাউজারের পপআপ ব্লকার চেক করুন

### সমস্যা: প্রিমিয়াম স্ট্যাটাস কাজ করছে না
**সমাধান**: `isPremiumUser()` ফাংশন সঠিকভাবে কাজ করছে কিনা চেক করুন

### সমস্যা: ডাউনলোড শুরু হচ্ছে না
**সমাধান**: ডাউনলোড URL সঠিক কিনা এবং CORS policy চেক করুন

### সমস্যা: CSS স্টাইল লোড হচ্ছে না
**সমাধান**: CSS ফাইলের path সঠিক কিনা চেক করুন

## বিজ্ঞাপন নেটওয়ার্ক (Ad Networks)

বর্তমানে সাপোর্টেড:
- highperformanceformat.com
- profitableratecpm.com

নতুন বিজ্ঞাপন নেটওয়ার্ক যোগ করতে `js/download-popunder.js` এর `showPopunder()` ফাংশন এডিট করুন।

## নিরাপত্তা (Security)

✅ XSS প্রতিরোধ  
✅ CSRF সুরক্ষা  
✅ Safe popup handling  
✅ Premium user validation  

## পারফরমেন্স (Performance)

- Lazy loading for ad scripts
- Minimal DOM manipulation
- Efficient event handling
- Mobile optimized

## আপডেট লগ (Update Log)

### v1.0.0 (বর্তমান)
- প্রাথমিক রিলিজ
- বেসিক পপ-আন্ডার সিস্টেম
- প্রিমিয়াম ব্যবহারকারী সাপোর্ট
- রেসপন্সিভ ডিজাইন

## সাপোর্ট (Support)

কোনো সমস্যা হলে:
1. Browser console চেক করুন (F12)
2. Network tab এ error দেখুন
3. CSS/JS ফাইল সঠিকভাবে লোড হচ্ছে কিনা চেক করুন

---

**নোট**: এই সিস্টেম শুধুমাত্র বৈধ ডাউনলোড লিংকের জন্য ব্যবহার করুন এবং স্থানীয় আইন মেনে চলুন।
